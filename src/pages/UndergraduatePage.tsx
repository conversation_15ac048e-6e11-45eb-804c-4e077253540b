import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useLanguage } from '../context/LanguageContext';
import EnhancedScholarshipCard from '../components/EnhancedScholarshipCard';
import ProfessionalSidebar from '../components/ProfessionalSidebar';
import { Pagination, Spin, Alert } from 'antd';

interface Scholarship {
  id: number;
  title: string;
  description: string;
  level: string;
  country: string;
  deadline: string;
  isOpen: boolean;
  thumbnail: string;
  fundingSource?: string;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

const UndergraduatePage: React.FC = () => {
  const { translations } = useLanguage();
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 12,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  });

  const fetchScholarships = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams({
        level: 'Licence',
        page: pagination.page.toString(),
        limit: pagination.limit.toString()
      });

      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/scholarships/search?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch scholarships');
      }

      const data = await response.json();
      
      if (data.success) {
        setScholarships(data.data || []);
        setPagination(prev => ({
          ...prev,
          total: data.pagination?.total || 0,
          totalPages: data.pagination?.totalPages || 0,
          hasNextPage: data.pagination?.hasNextPage || false,
          hasPreviousPage: data.pagination?.hasPreviousPage || false
        }));
      } else {
        throw new Error(data.message || 'Failed to fetch scholarships');
      }
    } catch (error) {
      console.error('Error fetching undergraduate scholarships:', error);
      setError('Erreur lors du chargement des bourses. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchScholarships();
  }, [pagination.page]);

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const sidebarConfig = {
    type: 'levels' as const,
    currentItem: 'Licence',
    limit: 10
  };

  return (
    <>
      <Helmet>
        <title>Bourses d'Études de Licence | Opportunités de Financement pour Étudiants de Premier Cycle</title>
        <meta name="description" content="Découvrez des centaines de bourses d'études pour étudiants de licence. Financez vos études de premier cycle avec des opportunités de bourses internationales et nationales." />
        <meta name="keywords" content="bourses licence, financement études, bourses premier cycle, aide financière étudiants" />
      </Helmet>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <div className="inline-flex items-center px-4 py-2 bg-blue-500/20 rounded-full text-blue-100 text-sm font-medium mb-6">
                <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
                {pagination.total} bourses disponibles
              </div>
              
              <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                Bourses d'Études de
                <span className="block text-yellow-300">Licence</span>
              </h1>
              
              <p className="text-xl md:text-2xl text-blue-100 max-w-4xl mx-auto mb-8 leading-relaxed">
                Découvrez des opportunités de financement exceptionnelles pour vos études de premier cycle. 
                Notre plateforme vous connecte avec des bourses d'études nationales et internationales 
                spécialement conçues pour les étudiants de licence.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-12">
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                  <div className="text-3xl font-bold text-yellow-300 mb-2">500+</div>
                  <div className="text-blue-100">Bourses Disponibles</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                  <div className="text-3xl font-bold text-yellow-300 mb-2">50+</div>
                  <div className="text-blue-100">Pays Partenaires</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                  <div className="text-3xl font-bold text-yellow-300 mb-2">95%</div>
                  <div className="text-blue-100">Taux de Réussite</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Main Content */}
            <div className="lg:w-2/3">
              {/* Info Section */}
              <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  Pourquoi Choisir une Bourse de Licence ?
                </h2>
                <div className="prose prose-blue max-w-none">
                  <p className="text-gray-600 leading-relaxed mb-4">
                    Les études de licence représentent la première étape cruciale de votre parcours académique supérieur. 
                    Avec les coûts d'éducation en constante augmentation, obtenir une bourse d'études peut transformer 
                    votre avenir académique et professionnel.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1">
                        <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">Financement Complet</h4>
                        <p className="text-gray-600 text-sm">Frais de scolarité, logement et frais de subsistance couverts</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-1">
                        <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">Excellence Académique</h4>
                        <p className="text-gray-600 text-sm">Accès aux meilleures universités mondiales</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Scholarships Grid */}
              <div className="mb-8">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900">
                    Bourses de Licence Disponibles
                  </h2>
                  <div className="text-sm text-gray-600">
                    {!loading && !error && `${pagination.total} résultats`}
                  </div>
                </div>

                {loading ? (
                  <div className="flex justify-center items-center py-16">
                    <Spin size="large" tip="Chargement des bourses..." />
                  </div>
                ) : error ? (
                  <Alert
                    message="Erreur"
                    description={error}
                    type="error"
                    showIcon
                    className="mb-6 rounded-xl shadow-md"
                  />
                ) : (
                  <>
                    <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-2">
                      {scholarships.map((scholarship, index) => (
                        <div key={scholarship.id} className="animate-fade-in" style={{ animationDelay: `${index * 0.1}s` }}>
                          <EnhancedScholarshipCard
                            id={scholarship.id}
                            title={scholarship.title}
                            thumbnail={scholarship.thumbnail}
                            deadline={scholarship.deadline}
                            isOpen={scholarship.isOpen}
                            level={scholarship.level}
                            country={scholarship.country}
                            fundingSource={scholarship.fundingSource}
                          />
                        </div>
                      ))}
                    </div>

                    {/* Pagination */}
                    {pagination.total > 0 && (
                      <div className="flex justify-center mt-12">
                        <Pagination
                          current={pagination.page}
                          total={pagination.total}
                          pageSize={pagination.limit}
                          onChange={handlePageChange}
                          showSizeChanger={false}
                          showQuickJumper
                          showTotal={(total) => `Total ${total} bourses`}
                          className="shadow-sm rounded-xl p-2 bg-white"
                        />
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Sidebar */}
            <ProfessionalSidebar 
              config={sidebarConfig}
              className="lg:w-1/3"
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default UndergraduatePage;
