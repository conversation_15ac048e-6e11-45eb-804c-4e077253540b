import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useLanguage } from '../context/LanguageContext';
import EnhancedScholarshipCard from '../components/EnhancedScholarshipCard';
import ProfessionalSidebar from '../components/ProfessionalSidebar';
import PageEndSuggestions from '../components/PageEndSuggestions';
import { Pagination, Spin, Alert } from 'antd';

interface Scholarship {
  id: number;
  title: string;
  description: string;
  level: string;
  country: string;
  deadline: string;
  isOpen: boolean;
  thumbnail: string;
  fundingSource?: string;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

const MasterPage: React.FC = () => {
  const { translations } = useLanguage();
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 12,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  });

  const fetchScholarships = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams({
        level: 'Master',
        page: pagination.page.toString(),
        limit: pagination.limit.toString()
      });

      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/scholarships/search?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch scholarships');
      }

      const data = await response.json();
      
      if (data.success) {
        setScholarships(data.data || []);
        setPagination(prev => ({
          ...prev,
          total: data.pagination?.total || 0,
          totalPages: data.pagination?.totalPages || 0,
          hasNextPage: data.pagination?.hasNextPage || false,
          hasPreviousPage: data.pagination?.hasPreviousPage || false
        }));
      } else {
        throw new Error(data.message || 'Failed to fetch scholarships');
      }
    } catch (error) {
      console.error('Error fetching master scholarships:', error);
      setError('Erreur lors du chargement des bourses. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchScholarships();
  }, [pagination.page]);

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const sidebarConfig = {
    type: 'levels' as const,
    currentItem: 'Master',
    limit: 10
  };

  return (
    <>
      <Helmet>
        <title>Bourses d'Études de Master | Financement pour Études Supérieures de 2ème Cycle</title>
        <meta name="description" content="Explorez des opportunités de bourses d'études pour programmes de Master. Financez vos études supérieures avec des bourses internationales prestigieuses." />
        <meta name="keywords" content="bourses master, financement master, bourses deuxième cycle, études supérieures" />
      </Helmet>

      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-purple-600 to-indigo-700 text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <div className="inline-flex items-center px-4 py-2 bg-purple-500/20 rounded-full text-purple-100 text-sm font-medium mb-6">
                <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
                {pagination.total} bourses disponibles
              </div>
              
              <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                Bourses d'Études de
                <span className="block text-yellow-300">Master</span>
              </h1>
              
              <p className="text-xl md:text-2xl text-purple-100 max-w-4xl mx-auto mb-8 leading-relaxed">
                Propulsez votre carrière avec des bourses d'études de Master prestigieuses. 
                Accédez aux meilleures universités mondiales et spécialisez-vous dans votre domaine 
                d'expertise avec un financement complet.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-12">
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                  <div className="text-3xl font-bold text-yellow-300 mb-2">300+</div>
                  <div className="text-purple-100">Programmes Master</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                  <div className="text-3xl font-bold text-yellow-300 mb-2">40+</div>
                  <div className="text-purple-100">Universités Partenaires</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                  <div className="text-3xl font-bold text-yellow-300 mb-2">€50K+</div>
                  <div className="text-purple-100">Financement Moyen</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Main Content */}
            <div className="lg:w-2/3">
              {/* Info Section */}
              <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  Pourquoi Poursuivre un Master avec une Bourse ?
                </h2>
                <div className="prose prose-purple max-w-none">
                  <p className="text-gray-600 leading-relaxed mb-4">
                    Un Master représente un investissement stratégique dans votre avenir professionnel. 
                    Avec une bourse d'études, vous pouvez vous concentrer pleinement sur l'excellence académique 
                    et la recherche, sans les contraintes financières.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mt-1">
                        <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">Spécialisation Avancée</h4>
                        <p className="text-gray-600 text-sm">Expertise approfondie dans votre domaine de choix</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center mt-1">
                        <svg className="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">Opportunités Carrière</h4>
                        <p className="text-gray-600 text-sm">Accès à des postes de direction et de recherche</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1">
                        <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">Réseau International</h4>
                        <p className="text-gray-600 text-sm">Connexions mondiales avec experts et professionnels</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mt-1">
                        <svg className="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">Innovation & Recherche</h4>
                        <p className="text-gray-600 text-sm">Participation à des projets de recherche de pointe</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Scholarships Grid */}
              <div className="mb-8">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900">
                    Bourses de Master Disponibles
                  </h2>
                  <div className="text-sm text-gray-600">
                    {!loading && !error && `${pagination.total} résultats`}
                  </div>
                </div>

                {loading ? (
                  <div className="flex justify-center items-center py-16">
                    <Spin size="large" tip="Chargement des bourses..." />
                  </div>
                ) : error ? (
                  <Alert
                    message="Erreur"
                    description={error}
                    type="error"
                    showIcon
                    className="mb-6 rounded-xl shadow-md"
                  />
                ) : (
                  <>
                    <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-2">
                      {scholarships.map((scholarship, index) => (
                        <div key={scholarship.id} className="animate-fade-in" style={{ animationDelay: `${index * 0.1}s` }}>
                          <EnhancedScholarshipCard
                            id={scholarship.id}
                            title={scholarship.title}
                            thumbnail={scholarship.thumbnail}
                            deadline={scholarship.deadline}
                            isOpen={scholarship.isOpen}
                            level={scholarship.level}
                            country={scholarship.country}
                            fundingSource={scholarship.fundingSource}
                          />
                        </div>
                      ))}
                    </div>

                    {/* Pagination */}
                    {pagination.total > 0 && (
                      <div className="flex justify-center mt-12">
                        <Pagination
                          current={pagination.page}
                          total={pagination.total}
                          pageSize={pagination.limit}
                          onChange={handlePageChange}
                          showSizeChanger={false}
                          showQuickJumper
                          showTotal={(total) => `Total ${total} bourses`}
                          className="shadow-sm rounded-xl p-2 bg-white"
                        />
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Sidebar */}
            <ProfessionalSidebar 
              config={sidebarConfig}
              className="lg:w-1/3"
            />
          </div>
        </div>

        {/* Page End Suggestions */}
        <PageEndSuggestions
          currentPageType="level"
          currentItem="Master"
        />
      </div>
    </>
  );
};

export default MasterPage;
