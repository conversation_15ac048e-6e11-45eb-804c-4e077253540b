import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';

interface SuggestionItem {
  id: number;
  title: string;
  thumbnail?: string;
  deadline?: string;
  isOpen?: boolean;
  country?: string;
  level?: string;
  type?: string;
  description?: string;
  slug?: string;
}

interface PageEndSuggestionsProps {
  currentPageType: 'scholarship' | 'country' | 'level' | 'opportunity';
  currentItem?: string;
  excludeId?: number;
  className?: string;
}

const PageEndSuggestions: React.FC<PageEndSuggestionsProps> = ({
  currentPageType,
  currentItem,
  excludeId,
  className = ''
}) => {
  const { translations } = useLanguage();
  const [suggestions, setSuggestions] = useState<SuggestionItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSuggestions();
  }, [currentPageType, currentItem, excludeId]);

  const fetchSuggestions = async () => {
    try {
      setLoading(true);
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';
      
      let endpoint = '';
      const params = new URLSearchParams();
      params.append('limit', '6');
      
      if (excludeId) {
        params.append('excludeId', excludeId.toString());
      }

      switch (currentPageType) {
        case 'scholarship':
          endpoint = `${apiUrl}/api/scholarships/latest`;
          break;
        case 'country':
          endpoint = `${apiUrl}/api/scholarships/latest`;
          if (currentItem) {
            params.append('excludeCountry', currentItem);
          }
          break;
        case 'level':
          endpoint = `${apiUrl}/api/scholarships/latest`;
          if (currentItem) {
            params.append('excludeLevel', currentItem);
          }
          break;
        case 'opportunity':
          endpoint = `${apiUrl}/api/opportunities/latest`;
          break;
        default:
          endpoint = `${apiUrl}/api/scholarships/latest`;
      }

      const response = await fetch(`${endpoint}?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setSuggestions(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching suggestions:', error);
    } finally {
      setLoading(false);
    }
  };

  const getItemLink = (item: SuggestionItem) => {
    switch (currentPageType) {
      case 'opportunity':
        return `/opportunities/${item.id}`;
      default:
        return `/scholarships/${item.id}`;
    }
  };

  const getSectionTitle = () => {
    switch (currentPageType) {
      case 'scholarship':
        return 'Autres Bourses Recommandées';
      case 'country':
        return 'Bourses dans d\'Autres Pays';
      case 'level':
        return 'Autres Niveaux d\'Études';
      case 'opportunity':
        return 'Autres Opportunités';
      default:
        return 'Suggestions pour Vous';
    }
  };

  if (loading || suggestions.length === 0) {
    return null;
  }

  return (
    <div className={`bg-gradient-to-br from-gray-50 to-blue-50 py-16 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            {getSectionTitle()}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Continuez votre exploration et découvrez d'autres opportunités qui pourraient vous intéresser
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {suggestions.map((item, index) => (
            <Link
              key={item.id}
              to={getItemLink(item)}
              className="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-blue-200 transform hover:-translate-y-1"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {/* Thumbnail */}
              <div className="relative h-48 bg-gradient-to-br from-blue-500 to-indigo-600 overflow-hidden">
                {item.thumbnail ? (
                  <img
                    src={item.thumbnail}
                    alt={item.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <div className="text-white text-6xl opacity-20">
                      {currentPageType === 'opportunity' ? '🚀' : '🎓'}
                    </div>
                  </div>
                )}
                
                {/* Status Badge */}
                {item.isOpen !== undefined && (
                  <div className="absolute top-4 right-4">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      item.isOpen 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      <span className={`w-1.5 h-1.5 rounded-full mr-1 ${
                        item.isOpen ? 'bg-green-400' : 'bg-red-400'
                      }`}></span>
                      {item.isOpen ? 'Ouvert' : 'Fermé'}
                    </span>
                  </div>
                )}

                {/* Country/Level Badge */}
                {(item.country || item.level) && (
                  <div className="absolute bottom-4 left-4">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white/90 text-gray-800 backdrop-blur-sm">
                      {item.country || item.level}
                    </span>
                  </div>
                )}
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors duration-200">
                  {item.title}
                </h3>
                
                {item.description && (
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {item.description}
                  </p>
                )}

                {/* Deadline */}
                {item.deadline && (
                  <div className="flex items-center text-sm text-gray-500 mb-4">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    Date limite: {new Date(item.deadline).toLocaleDateString('fr-FR')}
                  </div>
                )}

                {/* Call to Action */}
                <div className="flex items-center text-blue-600 text-sm font-medium group-hover:text-blue-700">
                  <span>Voir les détails</span>
                  <svg className="ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <Link
            to="/bourses"
            className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-xl text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            <span>Voir Toutes les Bourses</span>
            <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PageEndSuggestions;
