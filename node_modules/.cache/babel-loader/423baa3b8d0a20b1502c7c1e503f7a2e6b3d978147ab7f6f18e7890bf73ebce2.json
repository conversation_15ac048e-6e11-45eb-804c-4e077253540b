{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Pagination, Spin, Alert } from 'antd';\nimport { useLocation } from 'react-router-dom';\nimport EnhancedScholarshipCard from '../components/EnhancedScholarshipCard';\nimport ProfessionalSidebar from '../components/ProfessionalSidebar';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Scholarships = () => {\n  _s();\n  const location = useLocation();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('');\n  const [selectedCountry, setSelectedCountry] = useState('');\n  const [scholarships, setScholarships] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [pagination, setPagination] = useState({\n    total: 0,\n    page: 1,\n    limit: 9,\n    // Show 9 scholarships per page (3x3 grid)\n    totalPages: 0,\n    hasNextPage: false,\n    hasPreviousPage: false\n  });\n\n  // Read URL parameters on component mount\n  useEffect(() => {\n    const searchParams = new URLSearchParams(location.search);\n    const levelParam = searchParams.get('level');\n    const countryParam = searchParams.get('country');\n    if (levelParam) {\n      setSelectedLevel(levelParam);\n    }\n    if (countryParam) {\n      setSelectedCountry(countryParam);\n    }\n  }, [location.search]);\n\n  // Fetch scholarships with pagination and filters\n  useEffect(() => {\n    fetchScholarships();\n  }, [pagination.page, selectedLevel, selectedCountry, searchQuery]);\n  const fetchScholarships = async () => {\n    try {\n      setLoading(true);\n\n      // Build query parameters\n      const params = new URLSearchParams();\n      params.append('page', pagination.page.toString());\n      params.append('limit', pagination.limit.toString());\n      if (searchQuery) {\n        params.append('q', searchQuery);\n      }\n      if (selectedLevel) {\n        params.append('level', selectedLevel);\n      }\n      if (selectedCountry) {\n        params.append('country', selectedCountry);\n      }\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5001'}/api/scholarships/search?${params.toString()}`);\n      if (!response.ok) {\n        throw new Error('Failed to fetch scholarships');\n      }\n      const data = await response.json();\n      console.log('Scholarships search API response:', data);\n\n      // Handle the correct API response format: { success: true, data: [...], pagination: {...} }\n      const scholarshipsData = data.data || data.scholarships || [];\n      const paginationData = data.pagination || {};\n      setScholarships(scholarshipsData);\n      setPagination(paginationData || {\n        total: scholarshipsData.length || 0,\n        page: 1,\n        limit: 9,\n        totalPages: Math.ceil((scholarshipsData.length || 0) / 9),\n        hasNextPage: false,\n        hasPreviousPage: false\n      });\n      setError(null);\n    } catch (err) {\n      console.error('Error fetching scholarships:', err);\n      setError('Failed to load scholarships. Please try again later.');\n      setScholarships([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle page change\n  const handlePageChange = page => {\n    setPagination(prev => ({\n      ...prev,\n      page\n    }));\n    // Scroll to top when page changes\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative bg-gradient-to-br from-gray-900 via-primary-dark to-primary overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-gradient-to-r from-primary/80 to-primary-dark/80 mix-blend-multiply\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto py-16 px-4 sm:py-24 sm:px-6 lg:px-8 relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl font-extrabold text-white sm:text-5xl sm:tracking-tight lg:text-6xl animate-fade-in\",\n            children: \"Trouvez Votre Bourse Id\\xE9ale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-5 max-w-xl mx-auto text-xl text-white/80 animate-slide-up\",\n            children: \"Parcourez notre vaste collection de bourses et trouvez celle qui correspond \\xE0 vos objectifs acad\\xE9miques.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow-xl rounded-2xl p-8 mb-12 transform translate-y-0 animate-slide-up\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900\",\n            children: \"Filtrer les Bourses\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setSearchQuery('');\n              setSelectedLevel('');\n              setSelectedCountry('');\n              setPagination(prev => ({\n                ...prev,\n                page: 1\n              }));\n            },\n            className: \"text-sm text-primary hover:text-primary-dark font-medium flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-1\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), \"R\\xE9initialiser les filtres\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"search\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Recherche\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 relative rounded-md shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5 text-gray-400\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"search\",\n                id: \"search\",\n                value: searchQuery,\n                onChange: e => {\n                  setSearchQuery(e.target.value);\n                  setPagination(prev => ({\n                    ...prev,\n                    page: 1\n                  }));\n                },\n                className: \"pl-10 block w-full rounded-xl border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm\",\n                placeholder: \"Rechercher des bourses...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"level\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Niveau d'\\xC9tudes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"level\",\n              name: \"level\",\n              value: selectedLevel,\n              onChange: e => {\n                setSelectedLevel(e.target.value);\n                setPagination(prev => ({\n                  ...prev,\n                  page: 1\n                }));\n              },\n              className: \"mt-1 block w-full rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Tous les niveaux\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Licence\",\n                children: \"Licence\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Master\",\n                children: \"Master\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Doctorat\",\n                children: \"Doctorat\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Post-doctorat\",\n                children: \"Post-doctorat\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"country\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Pays\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"country\",\n              name: \"country\",\n              value: selectedCountry,\n              onChange: e => {\n                setSelectedCountry(e.target.value);\n                setPagination(prev => ({\n                  ...prev,\n                  page: 1\n                }));\n              },\n              className: \"mt-1 block w-full rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Tous les pays\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"France\",\n                children: \"France\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Canada\",\n                children: \"Canada\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Belgique\",\n                children: \"Belgique\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Suisse\",\n                children: \"Suisse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Maroc\",\n                children: \"Maroc\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Tunisie\",\n                children: \"Tunisie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"S\\xE9n\\xE9gal\",\n                children: \"S\\xE9n\\xE9gal\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"C\\xF4te d'Ivoire\",\n                children: \"C\\xF4te d'Ivoire\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"status\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Statut\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"status\",\n              name: \"status\",\n              className: \"mt-1 block w-full rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Tous les statuts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"open\",\n                children: \"Ouvertes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"closed\",\n                children: \"Ferm\\xE9es\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"urgent\",\n                children: \"Urgentes (< 7 jours)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col lg:flex-row gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:w-2/3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                children: \"R\\xE9sultats de recherche\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: !loading && !error && `${pagination.total} bourses trouvées`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 md:mt-0\",\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"recent\",\n                  children: \"Plus r\\xE9centes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"deadline\",\n                  children: \"Date limite proche\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"relevance\",\n                  children: \"Pertinence\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 13\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 9\n          }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center items-center py-16\",\n            children: /*#__PURE__*/_jsxDEV(Spin, {\n              size: \"large\",\n              tip: \"Chargement des bourses...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 11\n          }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n            message: \"Erreur\",\n            description: error,\n            type: \"error\",\n            showIcon: true,\n            className: \"mb-6 rounded-xl shadow-md\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 11\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3\",\n              children: scholarships.map((scholarship, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-fade-in\",\n                style: {\n                  animationDelay: `${index * 0.1}s`\n                },\n                children: /*#__PURE__*/_jsxDEV(EnhancedScholarshipCard, {\n                  id: scholarship.id,\n                  title: scholarship.title,\n                  thumbnail: scholarship.thumbnail,\n                  deadline: scholarship.deadline,\n                  isOpen: scholarship.isOpen,\n                  level: scholarship.level,\n                  country: scholarship.country,\n                  onClick: (id, slug) => window.location.href = slug ? `/bourse/${slug}` : `/scholarships/${id}`,\n                  index: index\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 19\n                }, this)\n              }, scholarship.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 13\n            }, this), scholarships.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-16 bg-gray-50 rounded-2xl shadow-sm border border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"mx-auto h-12 w-12 text-gray-400\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 1.5,\n                  d: \"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"mt-4 text-lg font-medium text-gray-900\",\n                children: \"Aucune bourse trouv\\xE9e\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-sm text-gray-500 max-w-md mx-auto\",\n                children: \"Essayez d'ajuster vos crit\\xE8res de recherche ou de filtrage pour trouver ce que vous cherchez.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setSearchQuery('');\n                  setSelectedLevel('');\n                  setSelectedCountry('');\n                  setPagination(prev => ({\n                    ...prev,\n                    page: 1\n                  }));\n                },\n                className: \"mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\",\n                children: \"R\\xE9initialiser les filtres\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this), pagination.total > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-center mt-12\",\n              children: /*#__PURE__*/_jsxDEV(Pagination, {\n                current: pagination.page,\n                total: pagination.total,\n                pageSize: pagination.limit,\n                onChange: handlePageChange,\n                showSizeChanger: false,\n                showQuickJumper: true,\n                showTotal: total => `Total ${total} bourses`,\n                className: \"shadow-sm rounded-xl p-2 bg-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ProfessionalSidebar, {\n          config: {\n            type: 'levels',\n            currentItem: selectedLevel || undefined,\n            limit: 10\n          },\n          className: \"lg:w-1/3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 5\n  }, this);\n};\n_s(Scholarships, \"6ePyox+ZYk0YOUy0AN9IK1VUS44=\", false, function () {\n  return [useLocation];\n});\n_c = Scholarships;\nexport default Scholarships;\nvar _c;\n$RefreshReg$(_c, \"Scholarships\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Pagination", "Spin", "<PERSON><PERSON>", "useLocation", "EnhancedScholarshipCard", "ProfessionalSidebar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Scholarships", "_s", "location", "searchQuery", "setSearch<PERSON>uery", "selectedLevel", "setSelectedLevel", "selectedCountry", "setSelectedCountry", "scholarships", "setScholarships", "loading", "setLoading", "error", "setError", "pagination", "setPagination", "total", "page", "limit", "totalPages", "hasNextPage", "hasPreviousPage", "searchParams", "URLSearchParams", "search", "levelParam", "get", "countryParam", "fetchScholarships", "params", "append", "toString", "response", "fetch", "process", "env", "REACT_APP_API_URL", "ok", "Error", "data", "json", "console", "log", "scholarshipsData", "paginationData", "length", "Math", "ceil", "err", "handlePageChange", "prev", "window", "scrollTo", "top", "behavior", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "htmlFor", "type", "name", "id", "value", "onChange", "e", "target", "placeholder", "size", "tip", "message", "description", "showIcon", "map", "scholarship", "index", "style", "animationDelay", "title", "thumbnail", "deadline", "isOpen", "level", "country", "slug", "href", "current", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "config", "currentItem", "undefined", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Pagination, Spin, Alert } from 'antd';\nimport { useLocation } from 'react-router-dom';\nimport EnhancedScholarshipCard from '../components/EnhancedScholarshipCard';\nimport ProfessionalSidebar from '../components/ProfessionalSidebar';\nimport PageEndSuggestions from '../components/PageEndSuggestions';\n\ninterface Scholarship {\n  id: number;\n  title: string;\n  description: string;\n  level: string;\n  country: string;\n  deadline: string;\n  isOpen: boolean;\n  thumbnail: string;\n}\n\ninterface PaginationData {\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n  hasNextPage: boolean;\n  hasPreviousPage: boolean;\n}\n\nconst Scholarships: React.FC = () => {\n  const location = useLocation();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('');\n  const [selectedCountry, setSelectedCountry] = useState('');\n  const [scholarships, setScholarships] = useState<Scholarship[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [pagination, setPagination] = useState<PaginationData>({\n    total: 0,\n    page: 1,\n    limit: 9, // Show 9 scholarships per page (3x3 grid)\n    totalPages: 0,\n    hasNextPage: false,\n    hasPreviousPage: false\n  });\n\n  // Read URL parameters on component mount\n  useEffect(() => {\n    const searchParams = new URLSearchParams(location.search);\n    const levelParam = searchParams.get('level');\n    const countryParam = searchParams.get('country');\n\n    if (levelParam) {\n      setSelectedLevel(levelParam);\n    }\n    if (countryParam) {\n      setSelectedCountry(countryParam);\n    }\n  }, [location.search]);\n\n  // Fetch scholarships with pagination and filters\n  useEffect(() => {\n    fetchScholarships();\n  }, [pagination.page, selectedLevel, selectedCountry, searchQuery]);\n\n  const fetchScholarships = async () => {\n    try {\n      setLoading(true);\n\n      // Build query parameters\n      const params = new URLSearchParams();\n      params.append('page', pagination.page.toString());\n      params.append('limit', pagination.limit.toString());\n\n      if (searchQuery) {\n        params.append('q', searchQuery);\n      }\n\n      if (selectedLevel) {\n        params.append('level', selectedLevel);\n      }\n\n      if (selectedCountry) {\n        params.append('country', selectedCountry);\n      }\n\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5001'}/api/scholarships/search?${params.toString()}`);\n\n      if (!response.ok) {\n        throw new Error('Failed to fetch scholarships');\n      }\n\n      const data = await response.json();\n      console.log('Scholarships search API response:', data);\n\n      // Handle the correct API response format: { success: true, data: [...], pagination: {...} }\n      const scholarshipsData = data.data || data.scholarships || [];\n      const paginationData = data.pagination || {};\n\n      setScholarships(scholarshipsData);\n      setPagination(paginationData || {\n        total: scholarshipsData.length || 0,\n        page: 1,\n        limit: 9,\n        totalPages: Math.ceil((scholarshipsData.length || 0) / 9),\n        hasNextPage: false,\n        hasPreviousPage: false\n      });\n      setError(null);\n    } catch (err) {\n      console.error('Error fetching scholarships:', err);\n      setError('Failed to load scholarships. Please try again later.');\n      setScholarships([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle page change\n  const handlePageChange = (page: number) => {\n    setPagination(prev => ({\n      ...prev,\n      page\n    }));\n    // Scroll to top when page changes\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  return (\n    <div className=\"bg-white min-h-screen\">\n      {/* Hero Section */}\n      <div className=\"relative bg-gradient-to-br from-gray-900 via-primary-dark to-primary overflow-hidden\">\n        {/* Background overlay */}\n        <div className=\"absolute inset-0 bg-gradient-to-r from-primary/80 to-primary-dark/80 mix-blend-multiply\" />\n\n        <div className=\"max-w-7xl mx-auto py-16 px-4 sm:py-24 sm:px-6 lg:px-8 relative z-10\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl font-extrabold text-white sm:text-5xl sm:tracking-tight lg:text-6xl animate-fade-in\">\n              Trouvez Votre Bourse Idéale\n            </h1>\n            <p className=\"mt-5 max-w-xl mx-auto text-xl text-white/80 animate-slide-up\">\n              Parcourez notre vaste collection de bourses et trouvez celle qui correspond à vos objectifs académiques.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Search and Filter Section */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-10\">\n        <div className=\"bg-white shadow-xl rounded-2xl p-8 mb-12 transform translate-y-0 animate-slide-up\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <h2 className=\"text-xl font-bold text-gray-900\">Filtrer les Bourses</h2>\n            <button\n              onClick={() => {\n                setSearchQuery('');\n                setSelectedLevel('');\n                setSelectedCountry('');\n                setPagination(prev => ({ ...prev, page: 1 }));\n              }}\n              className=\"text-sm text-primary hover:text-primary-dark font-medium flex items-center\"\n            >\n              <svg className=\"w-4 h-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n              </svg>\n              Réinitialiser les filtres\n            </button>\n          </div>\n          <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\">\n            <div>\n              <label htmlFor=\"search\" className=\"block text-sm font-medium text-gray-700\">\n                Recherche\n              </label>\n              <div className=\"mt-1 relative rounded-md shadow-sm\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                  </svg>\n                </div>\n                <input\n                  type=\"text\"\n                  name=\"search\"\n                  id=\"search\"\n                  value={searchQuery}\n                  onChange={(e) => {\n                    setSearchQuery(e.target.value);\n                    setPagination(prev => ({ ...prev, page: 1 }));\n                  }}\n                  className=\"pl-10 block w-full rounded-xl border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm\"\n                  placeholder=\"Rechercher des bourses...\"\n                />\n              </div>\n            </div>\n            <div>\n              <label htmlFor=\"level\" className=\"block text-sm font-medium text-gray-700\">\n                Niveau d'Études\n              </label>\n              <select\n                id=\"level\"\n                name=\"level\"\n                value={selectedLevel}\n                onChange={(e) => {\n                  setSelectedLevel(e.target.value);\n                  setPagination(prev => ({ ...prev, page: 1 }));\n                }}\n                className=\"mt-1 block w-full rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm\"\n              >\n                <option value=\"\">Tous les niveaux</option>\n                <option value=\"Licence\">Licence</option>\n                <option value=\"Master\">Master</option>\n                <option value=\"Doctorat\">Doctorat</option>\n                <option value=\"Post-doctorat\">Post-doctorat</option>\n              </select>\n            </div>\n            <div>\n              <label htmlFor=\"country\" className=\"block text-sm font-medium text-gray-700\">\n                Pays\n              </label>\n              <select\n                id=\"country\"\n                name=\"country\"\n                value={selectedCountry}\n                onChange={(e) => {\n                  setSelectedCountry(e.target.value);\n                  setPagination(prev => ({ ...prev, page: 1 }));\n                }}\n                className=\"mt-1 block w-full rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm\"\n              >\n                <option value=\"\">Tous les pays</option>\n                <option value=\"France\">France</option>\n                <option value=\"Canada\">Canada</option>\n                <option value=\"Belgique\">Belgique</option>\n                <option value=\"Suisse\">Suisse</option>\n                <option value=\"Maroc\">Maroc</option>\n                <option value=\"Tunisie\">Tunisie</option>\n                <option value=\"Sénégal\">Sénégal</option>\n                <option value=\"Côte d'Ivoire\">Côte d'Ivoire</option>\n              </select>\n            </div>\n            <div>\n              <label htmlFor=\"status\" className=\"block text-sm font-medium text-gray-700\">\n                Statut\n              </label>\n              <select\n                id=\"status\"\n                name=\"status\"\n                className=\"mt-1 block w-full rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm\"\n              >\n                <option value=\"\">Tous les statuts</option>\n                <option value=\"open\">Ouvertes</option>\n                <option value=\"closed\">Fermées</option>\n                <option value=\"urgent\">Urgentes (&lt; 7 jours)</option>\n              </select>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Content Section */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16\">\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:w-2/3\">\n        {/* Results header */}\n        <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Résultats de recherche</h2>\n            <p className=\"text-gray-600\">\n              {!loading && !error && `${pagination.total} bourses trouvées`}\n            </p>\n          </div>\n          <div className=\"mt-4 md:mt-0\">\n            <select\n              className=\"rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary text-sm\"\n            >\n              <option value=\"recent\">Plus récentes</option>\n              <option value=\"deadline\">Date limite proche</option>\n              <option value=\"relevance\">Pertinence</option>\n            </select>\n          </div>\n        </div>\n\n        {loading ? (\n          <div className=\"flex justify-center items-center py-16\">\n            <Spin size=\"large\" tip=\"Chargement des bourses...\" />\n          </div>\n        ) : error ? (\n          <Alert\n            message=\"Erreur\"\n            description={error}\n            type=\"error\"\n            showIcon\n            className=\"mb-6 rounded-xl shadow-md\"\n          />\n        ) : (\n          <>\n            <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3\">\n              {scholarships.map((scholarship, index) => (\n                <div key={scholarship.id} className=\"animate-fade-in\" style={{ animationDelay: `${index * 0.1}s` }}>\n                  <EnhancedScholarshipCard\n                    id={scholarship.id}\n                    title={scholarship.title}\n                    thumbnail={scholarship.thumbnail}\n                    deadline={scholarship.deadline}\n                    isOpen={scholarship.isOpen}\n                    level={scholarship.level}\n                    country={scholarship.country}\n                    onClick={(id, slug) => window.location.href = slug ? `/bourse/${slug}` : `/scholarships/${id}`}\n                    index={index}\n                  />\n                </div>\n              ))}\n            </div>\n\n            {/* No Results Message */}\n            {scholarships.length === 0 && (\n              <div className=\"text-center py-16 bg-gray-50 rounded-2xl shadow-sm border border-gray-100\">\n                <svg className=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                <h3 className=\"mt-4 text-lg font-medium text-gray-900\">Aucune bourse trouvée</h3>\n                <p className=\"mt-2 text-sm text-gray-500 max-w-md mx-auto\">\n                  Essayez d'ajuster vos critères de recherche ou de filtrage pour trouver ce que vous cherchez.\n                </p>\n                <button\n                  onClick={() => {\n                    setSearchQuery('');\n                    setSelectedLevel('');\n                    setSelectedCountry('');\n                    setPagination(prev => ({ ...prev, page: 1 }));\n                  }}\n                  className=\"mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n                >\n                  Réinitialiser les filtres\n                </button>\n              </div>\n            )}\n\n            {/* Pagination */}\n            {pagination.total > 0 && (\n              <div className=\"flex justify-center mt-12\">\n                <Pagination\n                  current={pagination.page}\n                  total={pagination.total}\n                  pageSize={pagination.limit}\n                  onChange={handlePageChange}\n                  showSizeChanger={false}\n                  showQuickJumper\n                  showTotal={(total) => `Total ${total} bourses`}\n                  className=\"shadow-sm rounded-xl p-2 bg-white\"\n                />\n              </div>\n            )}\n          </>\n        )}\n            </div>\n\n            {/* Sidebar */}\n            <ProfessionalSidebar\n              config={{\n                type: 'levels' as const,\n                currentItem: selectedLevel || undefined,\n                limit: 10\n              }}\n              className=\"lg:w-1/3\"\n            />\n          </div>\n        </div>\n      </div>\n    );\n};\n\nexport default Scholarships;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AAC9C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,uBAAuB,MAAM,uCAAuC;AAC3E,OAAOC,mBAAmB,MAAM,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAuBpE,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAiB;IAC3D6B,KAAK,EAAE,CAAC;IACRC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IAAE;IACVC,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE;EACnB,CAAC,CAAC;;EAEF;EACAjC,SAAS,CAAC,MAAM;IACd,MAAMkC,YAAY,GAAG,IAAIC,eAAe,CAACtB,QAAQ,CAACuB,MAAM,CAAC;IACzD,MAAMC,UAAU,GAAGH,YAAY,CAACI,GAAG,CAAC,OAAO,CAAC;IAC5C,MAAMC,YAAY,GAAGL,YAAY,CAACI,GAAG,CAAC,SAAS,CAAC;IAEhD,IAAID,UAAU,EAAE;MACdpB,gBAAgB,CAACoB,UAAU,CAAC;IAC9B;IACA,IAAIE,YAAY,EAAE;MAChBpB,kBAAkB,CAACoB,YAAY,CAAC;IAClC;EACF,CAAC,EAAE,CAAC1B,QAAQ,CAACuB,MAAM,CAAC,CAAC;;EAErB;EACApC,SAAS,CAAC,MAAM;IACdwC,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACd,UAAU,CAACG,IAAI,EAAEb,aAAa,EAAEE,eAAe,EAAEJ,WAAW,CAAC,CAAC;EAElE,MAAM0B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMkB,MAAM,GAAG,IAAIN,eAAe,CAAC,CAAC;MACpCM,MAAM,CAACC,MAAM,CAAC,MAAM,EAAEhB,UAAU,CAACG,IAAI,CAACc,QAAQ,CAAC,CAAC,CAAC;MACjDF,MAAM,CAACC,MAAM,CAAC,OAAO,EAAEhB,UAAU,CAACI,KAAK,CAACa,QAAQ,CAAC,CAAC,CAAC;MAEnD,IAAI7B,WAAW,EAAE;QACf2B,MAAM,CAACC,MAAM,CAAC,GAAG,EAAE5B,WAAW,CAAC;MACjC;MAEA,IAAIE,aAAa,EAAE;QACjByB,MAAM,CAACC,MAAM,CAAC,OAAO,EAAE1B,aAAa,CAAC;MACvC;MAEA,IAAIE,eAAe,EAAE;QACnBuB,MAAM,CAACC,MAAM,CAAC,SAAS,EAAExB,eAAe,CAAC;MAC3C;MAEA,MAAM0B,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,4BAA4BP,MAAM,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC;MAExI,IAAI,CAACC,QAAQ,CAACK,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;MACjD;MAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAClCC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEH,IAAI,CAAC;;MAEtD;MACA,MAAMI,gBAAgB,GAAGJ,IAAI,CAACA,IAAI,IAAIA,IAAI,CAAC/B,YAAY,IAAI,EAAE;MAC7D,MAAMoC,cAAc,GAAGL,IAAI,CAACzB,UAAU,IAAI,CAAC,CAAC;MAE5CL,eAAe,CAACkC,gBAAgB,CAAC;MACjC5B,aAAa,CAAC6B,cAAc,IAAI;QAC9B5B,KAAK,EAAE2B,gBAAgB,CAACE,MAAM,IAAI,CAAC;QACnC5B,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,UAAU,EAAE2B,IAAI,CAACC,IAAI,CAAC,CAACJ,gBAAgB,CAACE,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC;QACzDzB,WAAW,EAAE,KAAK;QAClBC,eAAe,EAAE;MACnB,CAAC,CAAC;MACFR,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOmC,GAAG,EAAE;MACZP,OAAO,CAAC7B,KAAK,CAAC,8BAA8B,EAAEoC,GAAG,CAAC;MAClDnC,QAAQ,CAAC,sDAAsD,CAAC;MAChEJ,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsC,gBAAgB,GAAIhC,IAAY,IAAK;IACzCF,aAAa,CAACmC,IAAI,KAAK;MACrB,GAAGA,IAAI;MACPjC;IACF,CAAC,CAAC,CAAC;IACH;IACAkC,MAAM,CAACC,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACjD,CAAC;EAED,oBACE1D,OAAA;IAAK2D,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAEpC5D,OAAA;MAAK2D,SAAS,EAAC,sFAAsF;MAAAC,QAAA,gBAEnG5D,OAAA;QAAK2D,SAAS,EAAC;MAAyF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE3GhE,OAAA;QAAK2D,SAAS,EAAC,qEAAqE;QAAAC,QAAA,eAClF5D,OAAA;UAAK2D,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B5D,OAAA;YAAI2D,SAAS,EAAC,8FAA8F;YAAAC,QAAA,EAAC;UAE7G;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhE,OAAA;YAAG2D,SAAS,EAAC,8DAA8D;YAAAC,QAAA,EAAC;UAE5E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhE,OAAA;MAAK2D,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5D5D,OAAA;QAAK2D,SAAS,EAAC,mFAAmF;QAAAC,QAAA,gBAChG5D,OAAA;UAAK2D,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD5D,OAAA;YAAI2D,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxEhE,OAAA;YACEiE,OAAO,EAAEA,CAAA,KAAM;cACb1D,cAAc,CAAC,EAAE,CAAC;cAClBE,gBAAgB,CAAC,EAAE,CAAC;cACpBE,kBAAkB,CAAC,EAAE,CAAC;cACtBQ,aAAa,CAACmC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEjC,IAAI,EAAE;cAAE,CAAC,CAAC,CAAC;YAC/C,CAAE;YACFsC,SAAS,EAAC,4EAA4E;YAAAC,QAAA,gBAEtF5D,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAACO,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAR,QAAA,eACjF5D,OAAA;gBAAMqE,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA6G;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClL,CAAC,gCAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNhE,OAAA;UAAK2D,SAAS,EAAC,sDAAsD;UAAAC,QAAA,gBACnE5D,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAOyE,OAAO,EAAC,QAAQ;cAACd,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE5E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhE,OAAA;cAAK2D,SAAS,EAAC,oCAAoC;cAAAC,QAAA,gBACjD5D,OAAA;gBAAK2D,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,eACnF5D,OAAA;kBAAK2D,SAAS,EAAC,uBAAuB;kBAACO,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACC,MAAM,EAAC,cAAc;kBAAAR,QAAA,eAC1F5D,OAAA;oBAAMqE,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAA6C;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNhE,OAAA;gBACE0E,IAAI,EAAC,MAAM;gBACXC,IAAI,EAAC,QAAQ;gBACbC,EAAE,EAAC,QAAQ;gBACXC,KAAK,EAAEvE,WAAY;gBACnBwE,QAAQ,EAAGC,CAAC,IAAK;kBACfxE,cAAc,CAACwE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;kBAC9B1D,aAAa,CAACmC,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEjC,IAAI,EAAE;kBAAE,CAAC,CAAC,CAAC;gBAC/C,CAAE;gBACFsC,SAAS,EAAC,4GAA4G;gBACtHsB,WAAW,EAAC;cAA2B;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAOyE,OAAO,EAAC,OAAO;cAACd,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhE,OAAA;cACE4E,EAAE,EAAC,OAAO;cACVD,IAAI,EAAC,OAAO;cACZE,KAAK,EAAErE,aAAc;cACrBsE,QAAQ,EAAGC,CAAC,IAAK;gBACftE,gBAAgB,CAACsE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;gBAChC1D,aAAa,CAACmC,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEjC,IAAI,EAAE;gBAAE,CAAC,CAAC,CAAC;cAC/C,CAAE;cACFsC,SAAS,EAAC,wJAAwJ;cAAAC,QAAA,gBAElK5D,OAAA;gBAAQ6E,KAAK,EAAC,EAAE;gBAAAjB,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1ChE,OAAA;gBAAQ6E,KAAK,EAAC,SAAS;gBAAAjB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxChE,OAAA;gBAAQ6E,KAAK,EAAC,QAAQ;gBAAAjB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtChE,OAAA;gBAAQ6E,KAAK,EAAC,UAAU;gBAAAjB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1ChE,OAAA;gBAAQ6E,KAAK,EAAC,eAAe;gBAAAjB,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNhE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAOyE,OAAO,EAAC,SAAS;cAACd,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE7E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhE,OAAA;cACE4E,EAAE,EAAC,SAAS;cACZD,IAAI,EAAC,SAAS;cACdE,KAAK,EAAEnE,eAAgB;cACvBoE,QAAQ,EAAGC,CAAC,IAAK;gBACfpE,kBAAkB,CAACoE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;gBAClC1D,aAAa,CAACmC,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEjC,IAAI,EAAE;gBAAE,CAAC,CAAC,CAAC;cAC/C,CAAE;cACFsC,SAAS,EAAC,wJAAwJ;cAAAC,QAAA,gBAElK5D,OAAA;gBAAQ6E,KAAK,EAAC,EAAE;gBAAAjB,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvChE,OAAA;gBAAQ6E,KAAK,EAAC,QAAQ;gBAAAjB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtChE,OAAA;gBAAQ6E,KAAK,EAAC,QAAQ;gBAAAjB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtChE,OAAA;gBAAQ6E,KAAK,EAAC,UAAU;gBAAAjB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1ChE,OAAA;gBAAQ6E,KAAK,EAAC,QAAQ;gBAAAjB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtChE,OAAA;gBAAQ6E,KAAK,EAAC,OAAO;gBAAAjB,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpChE,OAAA;gBAAQ6E,KAAK,EAAC,SAAS;gBAAAjB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxChE,OAAA;gBAAQ6E,KAAK,EAAC,eAAS;gBAAAjB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxChE,OAAA;gBAAQ6E,KAAK,EAAC,kBAAe;gBAAAjB,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNhE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAOyE,OAAO,EAAC,QAAQ;cAACd,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE5E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhE,OAAA;cACE4E,EAAE,EAAC,QAAQ;cACXD,IAAI,EAAC,QAAQ;cACbhB,SAAS,EAAC,wJAAwJ;cAAAC,QAAA,gBAElK5D,OAAA;gBAAQ6E,KAAK,EAAC,EAAE;gBAAAjB,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1ChE,OAAA;gBAAQ6E,KAAK,EAAC,MAAM;gBAAAjB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtChE,OAAA;gBAAQ6E,KAAK,EAAC,QAAQ;gBAAAjB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvChE,OAAA;gBAAQ6E,KAAK,EAAC,QAAQ;gBAAAjB,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhE,OAAA;MAAK2D,SAAS,EAAC,8CAA8C;MAAAC,QAAA,eAC3D5D,OAAA;QAAK2D,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAE9C5D,OAAA;UAAK2D,SAAS,EAAC,UAAU;UAAAC,QAAA,gBAE3B5D,OAAA;YAAK2D,SAAS,EAAC,4EAA4E;YAAAC,QAAA,gBACzF5D,OAAA;cAAA4D,QAAA,gBACE5D,OAAA;gBAAI2D,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjFhE,OAAA;gBAAG2D,SAAS,EAAC,eAAe;gBAAAC,QAAA,EACzB,CAAC9C,OAAO,IAAI,CAACE,KAAK,IAAI,GAAGE,UAAU,CAACE,KAAK;cAAmB;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNhE,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B5D,OAAA;gBACE2D,SAAS,EAAC,mIAAmI;gBAAAC,QAAA,gBAE7I5D,OAAA;kBAAQ6E,KAAK,EAAC,QAAQ;kBAAAjB,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC7ChE,OAAA;kBAAQ6E,KAAK,EAAC,UAAU;kBAAAjB,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpDhE,OAAA;kBAAQ6E,KAAK,EAAC,WAAW;kBAAAjB,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELlD,OAAO,gBACNd,OAAA;YAAK2D,SAAS,EAAC,wCAAwC;YAAAC,QAAA,eACrD5D,OAAA,CAACN,IAAI;cAACwF,IAAI,EAAC,OAAO;cAACC,GAAG,EAAC;YAA2B;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,GACJhD,KAAK,gBACPhB,OAAA,CAACL,KAAK;YACJyF,OAAO,EAAC,QAAQ;YAChBC,WAAW,EAAErE,KAAM;YACnB0D,IAAI,EAAC,OAAO;YACZY,QAAQ;YACR3B,SAAS,EAAC;UAA2B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,gBAEFhE,OAAA,CAAAE,SAAA;YAAA0D,QAAA,gBACE5D,OAAA;cAAK2D,SAAS,EAAC,sDAAsD;cAAAC,QAAA,EAClEhD,YAAY,CAAC2E,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBACnCzF,OAAA;gBAA0B2D,SAAS,EAAC,iBAAiB;gBAAC+B,KAAK,EAAE;kBAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;gBAAI,CAAE;gBAAA7B,QAAA,eACjG5D,OAAA,CAACH,uBAAuB;kBACtB+E,EAAE,EAAEY,WAAW,CAACZ,EAAG;kBACnBgB,KAAK,EAAEJ,WAAW,CAACI,KAAM;kBACzBC,SAAS,EAAEL,WAAW,CAACK,SAAU;kBACjCC,QAAQ,EAAEN,WAAW,CAACM,QAAS;kBAC/BC,MAAM,EAAEP,WAAW,CAACO,MAAO;kBAC3BC,KAAK,EAAER,WAAW,CAACQ,KAAM;kBACzBC,OAAO,EAAET,WAAW,CAACS,OAAQ;kBAC7BhC,OAAO,EAAEA,CAACW,EAAE,EAAEsB,IAAI,KAAK3C,MAAM,CAAClD,QAAQ,CAAC8F,IAAI,GAAGD,IAAI,GAAG,WAAWA,IAAI,EAAE,GAAG,iBAAiBtB,EAAE,EAAG;kBAC/Fa,KAAK,EAAEA;gBAAM;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC,GAXMwB,WAAW,CAACZ,EAAE;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYnB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAGLpD,YAAY,CAACqC,MAAM,KAAK,CAAC,iBACxBjD,OAAA;cAAK2D,SAAS,EAAC,2EAA2E;cAAAC,QAAA,gBACxF5D,OAAA;gBAAK2D,SAAS,EAAC,iCAAiC;gBAACO,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAAR,QAAA,eACpG5D,OAAA;kBAAMqE,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,GAAI;kBAACC,CAAC,EAAC;gBAAoF;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3J,CAAC,eACNhE,OAAA;gBAAI2D,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjFhE,OAAA;gBAAG2D,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,EAAC;cAE3D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJhE,OAAA;gBACEiE,OAAO,EAAEA,CAAA,KAAM;kBACb1D,cAAc,CAAC,EAAE,CAAC;kBAClBE,gBAAgB,CAAC,EAAE,CAAC;kBACpBE,kBAAkB,CAAC,EAAE,CAAC;kBACtBQ,aAAa,CAACmC,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEjC,IAAI,EAAE;kBAAE,CAAC,CAAC,CAAC;gBAC/C,CAAE;gBACFsC,SAAS,EAAC,+NAA+N;gBAAAC,QAAA,EAC1O;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,EAGA9C,UAAU,CAACE,KAAK,GAAG,CAAC,iBACnBpB,OAAA;cAAK2D,SAAS,EAAC,2BAA2B;cAAAC,QAAA,eACxC5D,OAAA,CAACP,UAAU;gBACT2G,OAAO,EAAElF,UAAU,CAACG,IAAK;gBACzBD,KAAK,EAAEF,UAAU,CAACE,KAAM;gBACxBiF,QAAQ,EAAEnF,UAAU,CAACI,KAAM;gBAC3BwD,QAAQ,EAAEzB,gBAAiB;gBAC3BiD,eAAe,EAAE,KAAM;gBACvBC,eAAe;gBACfC,SAAS,EAAGpF,KAAK,IAAK,SAASA,KAAK,UAAW;gBAC/CuC,SAAS,EAAC;cAAmC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA,eACD,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eAGNhE,OAAA,CAACF,mBAAmB;UAClB2G,MAAM,EAAE;YACN/B,IAAI,EAAE,QAAiB;YACvBgC,WAAW,EAAElG,aAAa,IAAImG,SAAS;YACvCrF,KAAK,EAAE;UACT,CAAE;UACFqC,SAAS,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEZ,CAAC;AAAC5D,EAAA,CApVID,YAAsB;EAAA,QACTP,WAAW;AAAA;AAAAgH,EAAA,GADxBzG,YAAsB;AAsV5B,eAAeA,YAAY;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}