{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/PageEndSuggestions.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useLanguage } from '../context/LanguageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PageEndSuggestions = ({\n  currentPageType,\n  currentItem,\n  excludeId,\n  className = ''\n}) => {\n  _s();\n  const {\n    translations\n  } = useLanguage();\n  const [suggestions, setSuggestions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    fetchSuggestions();\n  }, [currentPageType, currentItem, excludeId]);\n  const fetchSuggestions = async () => {\n    try {\n      setLoading(true);\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n      let endpoint = '';\n      const params = new URLSearchParams();\n      params.append('limit', '6');\n      if (excludeId) {\n        params.append('excludeId', excludeId.toString());\n      }\n      switch (currentPageType) {\n        case 'scholarship':\n          endpoint = `${apiUrl}/api/scholarships/latest`;\n          break;\n        case 'country':\n          endpoint = `${apiUrl}/api/scholarships/latest`;\n          if (currentItem) {\n            params.append('excludeCountry', currentItem);\n          }\n          break;\n        case 'level':\n          endpoint = `${apiUrl}/api/scholarships/latest`;\n          if (currentItem) {\n            params.append('excludeLevel', currentItem);\n          }\n          break;\n        case 'opportunity':\n          endpoint = `${apiUrl}/api/opportunities/latest`;\n          break;\n        default:\n          endpoint = `${apiUrl}/api/scholarships/latest`;\n      }\n      const response = await fetch(`${endpoint}?${params.toString()}`);\n      if (response.ok) {\n        const data = await response.json();\n        setSuggestions(data.data || []);\n      }\n    } catch (error) {\n      console.error('Error fetching suggestions:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getItemLink = item => {\n    switch (currentPageType) {\n      case 'opportunity':\n        return `/opportunities/${item.id}`;\n      default:\n        return `/scholarships/${item.id}`;\n    }\n  };\n  const getSectionTitle = () => {\n    switch (currentPageType) {\n      case 'scholarship':\n        return 'Autres Bourses Recommandées';\n      case 'country':\n        return 'Bourses dans d\\'Autres Pays';\n      case 'level':\n        return 'Autres Niveaux d\\'Études';\n      case 'opportunity':\n        return 'Autres Opportunités';\n      default:\n        return 'Suggestions pour Vous';\n    }\n  };\n  if (loading || suggestions.length === 0) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `bg-gradient-to-br from-gray-50 to-blue-50 py-16 ${className}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: getSectionTitle()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n          children: \"Continuez votre exploration et d\\xE9couvrez d'autres opportunit\\xE9s qui pourraient vous int\\xE9resser\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n        children: suggestions.map((item, index) => /*#__PURE__*/_jsxDEV(Link, {\n          to: getItemLink(item),\n          className: \"group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-blue-200 transform hover:-translate-y-1\",\n          style: {\n            animationDelay: `${index * 0.1}s`\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative h-48 bg-gradient-to-br from-blue-500 to-indigo-600 overflow-hidden\",\n            children: [item.thumbnail ? /*#__PURE__*/_jsxDEV(\"img\", {\n              src: item.thumbnail,\n              alt: item.title,\n              className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full h-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white text-6xl opacity-20\",\n                children: currentPageType === 'opportunity' ? '🚀' : '🎓'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 19\n            }, this), item.isOpen !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-4 right-4\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${item.isOpen ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `w-1.5 h-1.5 rounded-full mr-1 ${item.isOpen ? 'bg-green-400' : 'bg-red-400'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 23\n                }, this), item.isOpen ? 'Ouvert' : 'Fermé']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 19\n            }, this), (item.country || item.level) && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute bottom-4 left-4\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white/90 text-gray-800 backdrop-blur-sm\",\n                children: item.country || item.level\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors duration-200\",\n              children: item.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this), item.description && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm mb-4 line-clamp-2\",\n              children: item.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 19\n            }, this), item.deadline && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center text-sm text-gray-500 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-4 h-4 mr-2\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 21\n              }, this), \"Date limite: \", new Date(item.deadline).toLocaleDateString('fr-FR')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center text-blue-600 text-sm font-medium group-hover:text-blue-700\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Voir les d\\xE9tails\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M9 5l7 7-7 7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mt-12\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/bourses\",\n          className: \"inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-xl text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Voir Toutes les Bourses\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"ml-2 h-5 w-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 5\n  }, this);\n};\n_s(PageEndSuggestions, \"oZtn7q84A6Bgr1VR+CTGhrlohAs=\", false, function () {\n  return [useLanguage];\n});\n_c = PageEndSuggestions;\nexport default PageEndSuggestions;\nvar _c;\n$RefreshReg$(_c, \"PageEndSuggestions\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLanguage", "jsxDEV", "_jsxDEV", "PageEndSuggestions", "currentPageType", "currentItem", "excludeId", "className", "_s", "translations", "suggestions", "setSuggestions", "loading", "setLoading", "fetchSuggestions", "apiUrl", "process", "env", "REACT_APP_API_URL", "endpoint", "params", "URLSearchParams", "append", "toString", "response", "fetch", "ok", "data", "json", "error", "console", "getItemLink", "item", "id", "getSectionTitle", "length", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "index", "to", "style", "animationDelay", "thumbnail", "src", "alt", "title", "isOpen", "undefined", "country", "level", "description", "deadline", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "Date", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/PageEndSuggestions.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useLanguage } from '../context/LanguageContext';\n\ninterface SuggestionItem {\n  id: number;\n  title: string;\n  thumbnail?: string;\n  deadline?: string;\n  isOpen?: boolean;\n  country?: string;\n  level?: string;\n  type?: string;\n  description?: string;\n  slug?: string;\n}\n\ninterface PageEndSuggestionsProps {\n  currentPageType: 'scholarship' | 'country' | 'level' | 'opportunity';\n  currentItem?: string;\n  excludeId?: number;\n  className?: string;\n}\n\nconst PageEndSuggestions: React.FC<PageEndSuggestionsProps> = ({\n  currentPageType,\n  currentItem,\n  excludeId,\n  className = ''\n}) => {\n  const { translations } = useLanguage();\n  const [suggestions, setSuggestions] = useState<SuggestionItem[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchSuggestions();\n  }, [currentPageType, currentItem, excludeId]);\n\n  const fetchSuggestions = async () => {\n    try {\n      setLoading(true);\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n      \n      let endpoint = '';\n      const params = new URLSearchParams();\n      params.append('limit', '6');\n      \n      if (excludeId) {\n        params.append('excludeId', excludeId.toString());\n      }\n\n      switch (currentPageType) {\n        case 'scholarship':\n          endpoint = `${apiUrl}/api/scholarships/latest`;\n          break;\n        case 'country':\n          endpoint = `${apiUrl}/api/scholarships/latest`;\n          if (currentItem) {\n            params.append('excludeCountry', currentItem);\n          }\n          break;\n        case 'level':\n          endpoint = `${apiUrl}/api/scholarships/latest`;\n          if (currentItem) {\n            params.append('excludeLevel', currentItem);\n          }\n          break;\n        case 'opportunity':\n          endpoint = `${apiUrl}/api/opportunities/latest`;\n          break;\n        default:\n          endpoint = `${apiUrl}/api/scholarships/latest`;\n      }\n\n      const response = await fetch(`${endpoint}?${params.toString()}`);\n      if (response.ok) {\n        const data = await response.json();\n        setSuggestions(data.data || []);\n      }\n    } catch (error) {\n      console.error('Error fetching suggestions:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getItemLink = (item: SuggestionItem) => {\n    switch (currentPageType) {\n      case 'opportunity':\n        return `/opportunities/${item.id}`;\n      default:\n        return `/scholarships/${item.id}`;\n    }\n  };\n\n  const getSectionTitle = () => {\n    switch (currentPageType) {\n      case 'scholarship':\n        return 'Autres Bourses Recommandées';\n      case 'country':\n        return 'Bourses dans d\\'Autres Pays';\n      case 'level':\n        return 'Autres Niveaux d\\'Études';\n      case 'opportunity':\n        return 'Autres Opportunités';\n      default:\n        return 'Suggestions pour Vous';\n    }\n  };\n\n  if (loading || suggestions.length === 0) {\n    return null;\n  }\n\n  return (\n    <div className={`bg-gradient-to-br from-gray-50 to-blue-50 py-16 ${className}`}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            {getSectionTitle()}\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Continuez votre exploration et découvrez d'autres opportunités qui pourraient vous intéresser\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {suggestions.map((item, index) => (\n            <Link\n              key={item.id}\n              to={getItemLink(item)}\n              className=\"group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-blue-200 transform hover:-translate-y-1\"\n              style={{ animationDelay: `${index * 0.1}s` }}\n            >\n              {/* Thumbnail */}\n              <div className=\"relative h-48 bg-gradient-to-br from-blue-500 to-indigo-600 overflow-hidden\">\n                {item.thumbnail ? (\n                  <img\n                    src={item.thumbnail}\n                    alt={item.title}\n                    className=\"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                  />\n                ) : (\n                  <div className=\"w-full h-full flex items-center justify-center\">\n                    <div className=\"text-white text-6xl opacity-20\">\n                      {currentPageType === 'opportunity' ? '🚀' : '🎓'}\n                    </div>\n                  </div>\n                )}\n                \n                {/* Status Badge */}\n                {item.isOpen !== undefined && (\n                  <div className=\"absolute top-4 right-4\">\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                      item.isOpen \n                        ? 'bg-green-100 text-green-800' \n                        : 'bg-red-100 text-red-800'\n                    }`}>\n                      <span className={`w-1.5 h-1.5 rounded-full mr-1 ${\n                        item.isOpen ? 'bg-green-400' : 'bg-red-400'\n                      }`}></span>\n                      {item.isOpen ? 'Ouvert' : 'Fermé'}\n                    </span>\n                  </div>\n                )}\n\n                {/* Country/Level Badge */}\n                {(item.country || item.level) && (\n                  <div className=\"absolute bottom-4 left-4\">\n                    <span className=\"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white/90 text-gray-800 backdrop-blur-sm\">\n                      {item.country || item.level}\n                    </span>\n                  </div>\n                )}\n              </div>\n\n              {/* Content */}\n              <div className=\"p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors duration-200\">\n                  {item.title}\n                </h3>\n                \n                {item.description && (\n                  <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">\n                    {item.description}\n                  </p>\n                )}\n\n                {/* Deadline */}\n                {item.deadline && (\n                  <div className=\"flex items-center text-sm text-gray-500 mb-4\">\n                    <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                    </svg>\n                    Date limite: {new Date(item.deadline).toLocaleDateString('fr-FR')}\n                  </div>\n                )}\n\n                {/* Call to Action */}\n                <div className=\"flex items-center text-blue-600 text-sm font-medium group-hover:text-blue-700\">\n                  <span>Voir les détails</span>\n                  <svg className=\"ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                  </svg>\n                </div>\n              </div>\n            </Link>\n          ))}\n        </div>\n\n        {/* Call to Action */}\n        <div className=\"text-center mt-12\">\n          <Link\n            to=\"/bourses\"\n            className=\"inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-xl text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl\"\n          >\n            <span>Voir Toutes les Bourses</span>\n            <svg className=\"ml-2 h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7l5 5m0 0l-5 5m5-5H6\" />\n            </svg>\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PageEndSuggestions;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,WAAW,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAsBzD,MAAMC,kBAAqD,GAAGA,CAAC;EAC7DC,eAAe;EACfC,WAAW;EACXC,SAAS;EACTC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC;EAAa,CAAC,GAAGT,WAAW,CAAC,CAAC;EACtC,MAAM,CAACU,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAmB,EAAE,CAAC;EACpE,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACdgB,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACV,eAAe,EAAEC,WAAW,EAAEC,SAAS,CAAC,CAAC;EAE7C,MAAMQ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAME,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MAEvE,IAAIC,QAAQ,GAAG,EAAE;MACjB,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpCD,MAAM,CAACE,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC;MAE3B,IAAIhB,SAAS,EAAE;QACbc,MAAM,CAACE,MAAM,CAAC,WAAW,EAAEhB,SAAS,CAACiB,QAAQ,CAAC,CAAC,CAAC;MAClD;MAEA,QAAQnB,eAAe;QACrB,KAAK,aAAa;UAChBe,QAAQ,GAAG,GAAGJ,MAAM,0BAA0B;UAC9C;QACF,KAAK,SAAS;UACZI,QAAQ,GAAG,GAAGJ,MAAM,0BAA0B;UAC9C,IAAIV,WAAW,EAAE;YACfe,MAAM,CAACE,MAAM,CAAC,gBAAgB,EAAEjB,WAAW,CAAC;UAC9C;UACA;QACF,KAAK,OAAO;UACVc,QAAQ,GAAG,GAAGJ,MAAM,0BAA0B;UAC9C,IAAIV,WAAW,EAAE;YACfe,MAAM,CAACE,MAAM,CAAC,cAAc,EAAEjB,WAAW,CAAC;UAC5C;UACA;QACF,KAAK,aAAa;UAChBc,QAAQ,GAAG,GAAGJ,MAAM,2BAA2B;UAC/C;QACF;UACEI,QAAQ,GAAG,GAAGJ,MAAM,0BAA0B;MAClD;MAEA,MAAMS,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,QAAQ,IAAIC,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;MAChE,IAAIC,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClCjB,cAAc,CAACgB,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MACjC;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,WAAW,GAAIC,IAAoB,IAAK;IAC5C,QAAQ5B,eAAe;MACrB,KAAK,aAAa;QAChB,OAAO,kBAAkB4B,IAAI,CAACC,EAAE,EAAE;MACpC;QACE,OAAO,iBAAiBD,IAAI,CAACC,EAAE,EAAE;IACrC;EACF,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,QAAQ9B,eAAe;MACrB,KAAK,aAAa;QAChB,OAAO,6BAA6B;MACtC,KAAK,SAAS;QACZ,OAAO,6BAA6B;MACtC,KAAK,OAAO;QACV,OAAO,0BAA0B;MACnC,KAAK,aAAa;QAChB,OAAO,qBAAqB;MAC9B;QACE,OAAO,uBAAuB;IAClC;EACF,CAAC;EAED,IAAIQ,OAAO,IAAIF,WAAW,CAACyB,MAAM,KAAK,CAAC,EAAE;IACvC,OAAO,IAAI;EACb;EAEA,oBACEjC,OAAA;IAAKK,SAAS,EAAE,mDAAmDA,SAAS,EAAG;IAAA6B,QAAA,eAC7ElC,OAAA;MAAKK,SAAS,EAAC,wCAAwC;MAAA6B,QAAA,gBACrDlC,OAAA;QAAKK,SAAS,EAAC,mBAAmB;QAAA6B,QAAA,gBAChClC,OAAA;UAAIK,SAAS,EAAC,uCAAuC;UAAA6B,QAAA,EAClDF,eAAe,CAAC;QAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACLtC,OAAA;UAAGK,SAAS,EAAC,yCAAyC;UAAA6B,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENtC,OAAA;QAAKK,SAAS,EAAC,sDAAsD;QAAA6B,QAAA,EAClE1B,WAAW,CAAC+B,GAAG,CAAC,CAACT,IAAI,EAAEU,KAAK,kBAC3BxC,OAAA,CAACH,IAAI;UAEH4C,EAAE,EAAEZ,WAAW,CAACC,IAAI,CAAE;UACtBzB,SAAS,EAAC,8KAA8K;UACxLqC,KAAK,EAAE;YAAEC,cAAc,EAAE,GAAGH,KAAK,GAAG,GAAG;UAAI,CAAE;UAAAN,QAAA,gBAG7ClC,OAAA;YAAKK,SAAS,EAAC,6EAA6E;YAAA6B,QAAA,GACzFJ,IAAI,CAACc,SAAS,gBACb5C,OAAA;cACE6C,GAAG,EAAEf,IAAI,CAACc,SAAU;cACpBE,GAAG,EAAEhB,IAAI,CAACiB,KAAM;cAChB1C,SAAS,EAAC;YAAoF;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC,gBAEFtC,OAAA;cAAKK,SAAS,EAAC,gDAAgD;cAAA6B,QAAA,eAC7DlC,OAAA;gBAAKK,SAAS,EAAC,gCAAgC;gBAAA6B,QAAA,EAC5ChC,eAAe,KAAK,aAAa,GAAG,IAAI,GAAG;cAAI;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAGAR,IAAI,CAACkB,MAAM,KAAKC,SAAS,iBACxBjD,OAAA;cAAKK,SAAS,EAAC,wBAAwB;cAAA6B,QAAA,eACrClC,OAAA;gBAAMK,SAAS,EAAE,2EACfyB,IAAI,CAACkB,MAAM,GACP,6BAA6B,GAC7B,yBAAyB,EAC5B;gBAAAd,QAAA,gBACDlC,OAAA;kBAAMK,SAAS,EAAE,iCACfyB,IAAI,CAACkB,MAAM,GAAG,cAAc,GAAG,YAAY;gBAC1C;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACVR,IAAI,CAACkB,MAAM,GAAG,QAAQ,GAAG,OAAO;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,EAGA,CAACR,IAAI,CAACoB,OAAO,IAAIpB,IAAI,CAACqB,KAAK,kBAC1BnD,OAAA;cAAKK,SAAS,EAAC,0BAA0B;cAAA6B,QAAA,eACvClC,OAAA;gBAAMK,SAAS,EAAC,gHAAgH;gBAAA6B,QAAA,EAC7HJ,IAAI,CAACoB,OAAO,IAAIpB,IAAI,CAACqB;cAAK;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNtC,OAAA;YAAKK,SAAS,EAAC,KAAK;YAAA6B,QAAA,gBAClBlC,OAAA;cAAIK,SAAS,EAAC,gHAAgH;cAAA6B,QAAA,EAC3HJ,IAAI,CAACiB;YAAK;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EAEJR,IAAI,CAACsB,WAAW,iBACfpD,OAAA;cAAGK,SAAS,EAAC,yCAAyC;cAAA6B,QAAA,EACnDJ,IAAI,CAACsB;YAAW;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CACJ,EAGAR,IAAI,CAACuB,QAAQ,iBACZrD,OAAA;cAAKK,SAAS,EAAC,8CAA8C;cAAA6B,QAAA,gBAC3DlC,OAAA;gBAAKK,SAAS,EAAC,cAAc;gBAACiD,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAtB,QAAA,eACjFlC,OAAA;kBAAMyD,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAwF;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7J,CAAC,iBACO,EAAC,IAAIuB,IAAI,CAAC/B,IAAI,CAACuB,QAAQ,CAAC,CAACS,kBAAkB,CAAC,OAAO,CAAC;YAAA;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CACN,eAGDtC,OAAA;cAAKK,SAAS,EAAC,+EAA+E;cAAA6B,QAAA,gBAC5FlC,OAAA;gBAAAkC,QAAA,EAAM;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7BtC,OAAA;gBAAKK,SAAS,EAAC,0EAA0E;gBAACiD,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAtB,QAAA,eAC7IlC,OAAA;kBAAMyD,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAc;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GA5EDR,IAAI,CAACC,EAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6ER,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNtC,OAAA;QAAKK,SAAS,EAAC,mBAAmB;QAAA6B,QAAA,eAChClC,OAAA,CAACH,IAAI;UACH4C,EAAE,EAAC,UAAU;UACbpC,SAAS,EAAC,+QAA+Q;UAAA6B,QAAA,gBAEzRlC,OAAA;YAAAkC,QAAA,EAAM;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpCtC,OAAA;YAAKK,SAAS,EAAC,cAAc;YAACiD,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAtB,QAAA,eACjFlC,OAAA;cAAMyD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAA0B;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChC,EAAA,CAzMIL,kBAAqD;EAAA,QAMhCH,WAAW;AAAA;AAAAiE,EAAA,GANhC9D,kBAAqD;AA2M3D,eAAeA,kBAAkB;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}