{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Countries.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useLanguage } from '../context/LanguageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Countries = () => {\n  _s();\n  const {\n    translations\n  } = useLanguage();\n  const [countries, setCountries] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredCountries, setFilteredCountries] = useState([]);\n  useEffect(() => {\n    fetchCountries();\n  }, []);\n  useEffect(() => {\n    if (searchTerm.trim() === '') {\n      setFilteredCountries(countries);\n    } else {\n      const filtered = countries.filter(country => country.country.toLowerCase().includes(searchTerm.toLowerCase()));\n      setFilteredCountries(filtered);\n    }\n  }, [searchTerm, countries]);\n  const fetchCountries = async () => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n      const response = await fetch(`${apiUrl}/api/countries`);\n      if (response.ok) {\n        const data = await response.json();\n        setCountries(data.data || []);\n      } else {\n        console.error('Failed to fetch countries');\n      }\n    } catch (error) {\n      console.error('Error fetching countries:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getCountryFlag = countryName => {\n    const flagMap = {\n      'France': '🇫🇷',\n      'Germany': '🇩🇪',\n      'United Kingdom': '🇬🇧',\n      'United States': '🇺🇸',\n      'Canada': '🇨🇦',\n      'Australia': '🇦🇺',\n      'Netherlands': '🇳🇱',\n      'Sweden': '🇸🇪',\n      'Norway': '🇳🇴',\n      'Denmark': '🇩🇰',\n      'Switzerland': '🇨🇭',\n      'Belgium': '🇧🇪',\n      'Austria': '🇦🇹',\n      'Italy': '🇮🇹',\n      'Spain': '🇪🇸',\n      'Japan': '🇯🇵',\n      'South Korea': '🇰🇷',\n      'Singapore': '🇸🇬',\n      'New Zealand': '🇳🇿',\n      'Finland': '🇫🇮'\n    };\n    return flagMap[countryName] || '🌍';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 pt-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-4 text-gray-600\",\n            children: \"Chargement des pays...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl md:text-5xl font-bold mb-4\",\n            children: translations.countries.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-blue-100 max-w-3xl mx-auto\",\n            children: translations.countries.subtitle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-md mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: translations.countries.searchPlaceholder,\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-5 w-5 text-gray-400\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16\",\n      children: filteredCountries.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\uD83D\\uDD0D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold text-gray-900 mb-2\",\n          children: \"Aucun pays trouv\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Essayez de modifier votre recherche ou parcourez tous les pays disponibles.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n        children: filteredCountries.map(country => /*#__PURE__*/_jsxDEV(Link, {\n          to: `/countries/${encodeURIComponent(country.country)}`,\n          className: \"group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-blue-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-4xl\",\n                children: getCountryFlag(country.country)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-blue-600\",\n                  children: country.count\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-500\",\n                  children: country.count === 1 ? 'bourse' : 'bourses'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 mb-2\",\n              children: country.country\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center text-blue-600 text-sm font-medium\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: translations.countries.viewScholarships\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M9 5l7 7-7 7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 17\n          }, this)\n        }, country.country, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white py-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-bold text-gray-900 mb-4\",\n            children: \"Statistiques Globales\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600\",\n            children: \"D\\xE9couvrez la r\\xE9partition des bourses par r\\xE9gion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl font-bold text-blue-600 mb-2\",\n              children: countries.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg text-gray-600\",\n              children: \"Pays disponibles\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl font-bold text-green-600 mb-2\",\n              children: countries.reduce((total, country) => total + country.count, 0)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg text-gray-600\",\n              children: \"Total des bourses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl font-bold text-purple-600 mb-2\",\n              children: Math.round(countries.reduce((total, country) => total + country.count, 0) / countries.length) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg text-gray-600\",\n              children: \"Moyenne par pays\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n};\n_s(Countries, \"FVd9G+D4BO3Hc9pyEiBVjSG+Opo=\", false, function () {\n  return [useLanguage];\n});\n_c = Countries;\nexport default Countries;\nvar _c;\n$RefreshReg$(_c, \"Countries\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLanguage", "jsxDEV", "_jsxDEV", "Countries", "_s", "translations", "countries", "setCountries", "loading", "setLoading", "searchTerm", "setSearchTerm", "filteredCountries", "setFilteredCountries", "fetchCountries", "trim", "filtered", "filter", "country", "toLowerCase", "includes", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "fetch", "ok", "data", "json", "console", "error", "getCountryFlag", "countryName", "flagMap", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "subtitle", "type", "placeholder", "searchPlaceholder", "value", "onChange", "e", "target", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "length", "map", "to", "encodeURIComponent", "count", "viewScholarships", "reduce", "total", "Math", "round", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Countries.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useLanguage } from '../context/LanguageContext';\n\ninterface Country {\n  country: string;\n  count: number;\n}\n\nconst Countries: React.FC = () => {\n  const { translations } = useLanguage();\n  const [countries, setCountries] = useState<Country[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredCountries, setFilteredCountries] = useState<Country[]>([]);\n\n  useEffect(() => {\n    fetchCountries();\n  }, []);\n\n  useEffect(() => {\n    if (searchTerm.trim() === '') {\n      setFilteredCountries(countries);\n    } else {\n      const filtered = countries.filter(country =>\n        country.country.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n      setFilteredCountries(filtered);\n    }\n  }, [searchTerm, countries]);\n\n  const fetchCountries = async () => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n      const response = await fetch(`${apiUrl}/api/countries`);\n      if (response.ok) {\n        const data = await response.json();\n        setCountries(data.data || []);\n      } else {\n        console.error('Failed to fetch countries');\n      }\n    } catch (error) {\n      console.error('Error fetching countries:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getCountryFlag = (countryName: string): string => {\n    const flagMap: { [key: string]: string } = {\n      'France': '🇫🇷',\n      'Germany': '🇩🇪',\n      'United Kingdom': '🇬🇧',\n      'United States': '🇺🇸',\n      'Canada': '🇨🇦',\n      'Australia': '🇦🇺',\n      'Netherlands': '🇳🇱',\n      'Sweden': '🇸🇪',\n      'Norway': '🇳🇴',\n      'Denmark': '🇩🇰',\n      'Switzerland': '🇨🇭',\n      'Belgium': '🇧🇪',\n      'Austria': '🇦🇹',\n      'Italy': '🇮🇹',\n      'Spain': '🇪🇸',\n      'Japan': '🇯🇵',\n      'South Korea': '🇰🇷',\n      'Singapore': '🇸🇬',\n      'New Zealand': '🇳🇿',\n      'Finland': '🇫🇮',\n    };\n    return flagMap[countryName] || '🌍';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 pt-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600\">Chargement des pays...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Hero Section */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-5xl font-bold mb-4\">\n              {translations.countries.title}\n            </h1>\n            <p className=\"text-xl text-blue-100 max-w-3xl mx-auto\">\n              {translations.countries.subtitle}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Search Section */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"max-w-md mx-auto\">\n          <div className=\"relative\">\n            <input\n              type=\"text\"\n              placeholder={translations.countries.searchPlaceholder}\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n              </svg>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Countries Grid */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16\">\n        {filteredCountries.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">🔍</div>\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n              Aucun pays trouvé\n            </h3>\n            <p className=\"text-gray-600\">\n              Essayez de modifier votre recherche ou parcourez tous les pays disponibles.\n            </p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {filteredCountries.map((country) => (\n              <Link\n                key={country.country}\n                to={`/countries/${encodeURIComponent(country.country)}`}\n                className=\"group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-blue-200\"\n              >\n                <div className=\"p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"text-4xl\">\n                      {getCountryFlag(country.country)}\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-2xl font-bold text-blue-600\">\n                        {country.count}\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        {country.count === 1 ? 'bourse' : 'bourses'}\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <h3 className=\"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 mb-2\">\n                    {country.country}\n                  </h3>\n                  \n                  <div className=\"flex items-center text-blue-600 text-sm font-medium\">\n                    <span>{translations.countries.viewScholarships}</span>\n                    <svg className=\"ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  </div>\n                </div>\n              </Link>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Statistics Section */}\n      <div className=\"bg-white py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Statistiques Globales\n            </h2>\n            <p className=\"text-lg text-gray-600\">\n              Découvrez la répartition des bourses par région\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl font-bold text-blue-600 mb-2\">\n                {countries.length}\n              </div>\n              <div className=\"text-lg text-gray-600\">\n                Pays disponibles\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-4xl font-bold text-green-600 mb-2\">\n                {countries.reduce((total, country) => total + country.count, 0)}\n              </div>\n              <div className=\"text-lg text-gray-600\">\n                Total des bourses\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-4xl font-bold text-purple-600 mb-2\">\n                {Math.round(countries.reduce((total, country) => total + country.count, 0) / countries.length) || 0}\n              </div>\n              <div className=\"text-lg text-gray-600\">\n                Moyenne par pays\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Countries;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,WAAW,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOzD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAa,CAAC,GAAGL,WAAW,CAAC,CAAC;EACtC,MAAM,CAACM,SAAS,EAAEC,YAAY,CAAC,GAAGV,QAAQ,CAAY,EAAE,CAAC;EACzD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACe,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhB,QAAQ,CAAY,EAAE,CAAC;EAEzEC,SAAS,CAAC,MAAM;IACdgB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAENhB,SAAS,CAAC,MAAM;IACd,IAAIY,UAAU,CAACK,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC5BF,oBAAoB,CAACP,SAAS,CAAC;IACjC,CAAC,MAAM;MACL,MAAMU,QAAQ,GAAGV,SAAS,CAACW,MAAM,CAACC,OAAO,IACvCA,OAAO,CAACA,OAAO,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACV,UAAU,CAACS,WAAW,CAAC,CAAC,CACjE,CAAC;MACDN,oBAAoB,CAACG,QAAQ,CAAC;IAChC;EACF,CAAC,EAAE,CAACN,UAAU,EAAEJ,SAAS,CAAC,CAAC;EAE3B,MAAMQ,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMO,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,MAAM,gBAAgB,CAAC;MACvD,IAAII,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClCtB,YAAY,CAACqB,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MAC/B,CAAC,MAAM;QACLE,OAAO,CAACC,KAAK,CAAC,2BAA2B,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuB,cAAc,GAAIC,WAAmB,IAAa;IACtD,MAAMC,OAAkC,GAAG;MACzC,QAAQ,EAAE,MAAM;MAChB,SAAS,EAAE,MAAM;MACjB,gBAAgB,EAAE,MAAM;MACxB,eAAe,EAAE,MAAM;MACvB,QAAQ,EAAE,MAAM;MAChB,WAAW,EAAE,MAAM;MACnB,aAAa,EAAE,MAAM;MACrB,QAAQ,EAAE,MAAM;MAChB,QAAQ,EAAE,MAAM;MAChB,SAAS,EAAE,MAAM;MACjB,aAAa,EAAE,MAAM;MACrB,SAAS,EAAE,MAAM;MACjB,SAAS,EAAE,MAAM;MACjB,OAAO,EAAE,MAAM;MACf,OAAO,EAAE,MAAM;MACf,OAAO,EAAE,MAAM;MACf,aAAa,EAAE,MAAM;MACrB,WAAW,EAAE,MAAM;MACnB,aAAa,EAAE,MAAM;MACrB,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,OAAO,CAACD,WAAW,CAAC,IAAI,IAAI;EACrC,CAAC;EAED,IAAIzB,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKiC,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9ElC,OAAA;QAAKiC,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC3DlC,OAAA;UAAKiC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BlC,OAAA;YAAKiC,SAAS,EAAC;UAAwE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9FtC,OAAA;YAAGiC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEtC,OAAA;IAAKiC,SAAS,EAAC,2DAA2D;IAAAC,QAAA,gBAExElC,OAAA;MAAKiC,SAAS,EAAC,+DAA+D;MAAAC,QAAA,eAC5ElC,OAAA;QAAKiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDlC,OAAA;UAAKiC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BlC,OAAA;YAAIiC,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAChD/B,YAAY,CAACC,SAAS,CAACmC;UAAK;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACLtC,OAAA;YAAGiC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EACnD/B,YAAY,CAACC,SAAS,CAACoC;UAAQ;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA;MAAKiC,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DlC,OAAA;QAAKiC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BlC,OAAA;UAAKiC,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBlC,OAAA;YACEyC,IAAI,EAAC,MAAM;YACXC,WAAW,EAAEvC,YAAY,CAACC,SAAS,CAACuC,iBAAkB;YACtDC,KAAK,EAAEpC,UAAW;YAClBqC,QAAQ,EAAGC,CAAC,IAAKrC,aAAa,CAACqC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CX,SAAS,EAAC;UAAoH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/H,CAAC,eACFtC,OAAA;YAAKiC,SAAS,EAAC,sEAAsE;YAAAC,QAAA,eACnFlC,OAAA;cAAKiC,SAAS,EAAC,uBAAuB;cAACe,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAhB,QAAA,eAC1FlC,OAAA;gBAAMmD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA6C;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA;MAAKiC,SAAS,EAAC,8CAA8C;MAAAC,QAAA,EAC1DxB,iBAAiB,CAAC6C,MAAM,KAAK,CAAC,gBAC7BvD,OAAA;QAAKiC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChClC,OAAA;UAAKiC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvCtC,OAAA;UAAIiC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAEzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtC,OAAA;UAAGiC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE7B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,gBAENtC,OAAA;QAAKiC,SAAS,EAAC,qEAAqE;QAAAC,QAAA,EACjFxB,iBAAiB,CAAC8C,GAAG,CAAExC,OAAO,iBAC7BhB,OAAA,CAACH,IAAI;UAEH4D,EAAE,EAAE,cAAcC,kBAAkB,CAAC1C,OAAO,CAACA,OAAO,CAAC,EAAG;UACxDiB,SAAS,EAAC,8IAA8I;UAAAC,QAAA,eAExJlC,OAAA;YAAKiC,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBlC,OAAA;cAAKiC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDlC,OAAA;gBAAKiC,SAAS,EAAC,UAAU;gBAAAC,QAAA,EACtBJ,cAAc,CAACd,OAAO,CAACA,OAAO;cAAC;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACNtC,OAAA;gBAAKiC,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlC,OAAA;kBAAKiC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAC9ClB,OAAO,CAAC2C;gBAAK;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACNtC,OAAA;kBAAKiC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EACnClB,OAAO,CAAC2C,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAG;gBAAS;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENtC,OAAA;cAAIiC,SAAS,EAAC,mGAAmG;cAAAC,QAAA,EAC9GlB,OAAO,CAACA;YAAO;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eAELtC,OAAA;cAAKiC,SAAS,EAAC,qDAAqD;cAAAC,QAAA,gBAClElC,OAAA;gBAAAkC,QAAA,EAAO/B,YAAY,CAACC,SAAS,CAACwD;cAAgB;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtDtC,OAAA;gBAAKiC,SAAS,EAAC,0EAA0E;gBAACe,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAhB,QAAA,eAC7IlC,OAAA;kBAAMmD,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAc;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GA7BDtB,OAAO,CAACA,OAAO;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8BhB,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNtC,OAAA;MAAKiC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BlC,OAAA;QAAKiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDlC,OAAA;UAAKiC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClC,OAAA;YAAIiC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtC,OAAA;YAAGiC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENtC,OAAA;UAAKiC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDlC,OAAA;YAAKiC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlC,OAAA;cAAKiC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACnD9B,SAAS,CAACmD;YAAM;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACNtC,OAAA;cAAKiC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAEvC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtC,OAAA;YAAKiC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlC,OAAA;cAAKiC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EACpD9B,SAAS,CAACyD,MAAM,CAAC,CAACC,KAAK,EAAE9C,OAAO,KAAK8C,KAAK,GAAG9C,OAAO,CAAC2C,KAAK,EAAE,CAAC;YAAC;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACNtC,OAAA;cAAKiC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAEvC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtC,OAAA;YAAKiC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlC,OAAA;cAAKiC,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EACrD6B,IAAI,CAACC,KAAK,CAAC5D,SAAS,CAACyD,MAAM,CAAC,CAACC,KAAK,EAAE9C,OAAO,KAAK8C,KAAK,GAAG9C,OAAO,CAAC2C,KAAK,EAAE,CAAC,CAAC,GAAGvD,SAAS,CAACmD,MAAM,CAAC,IAAI;YAAC;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChG,CAAC,eACNtC,OAAA;cAAKiC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAEvC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpC,EAAA,CAlNID,SAAmB;EAAA,QACEH,WAAW;AAAA;AAAAmE,EAAA,GADhChE,SAAmB;AAoNzB,eAAeA,SAAS;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}