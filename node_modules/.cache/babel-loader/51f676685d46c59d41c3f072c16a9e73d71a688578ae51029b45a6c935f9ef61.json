{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { Card, Statistic, Row, Col, Button, Modal, Form, Input, Select, message, Layout } from 'antd';\nimport { BookOutlined, MessageOutlined, UserOutlined, PlusOutlined, TeamOutlined, MailOutlined, AreaChartOutlined } from '@ant-design/icons';\nimport { useLanguage } from '../../context/LanguageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header\n} = Layout;\nconst AdminDashboard = () => {\n  _s();\n  const {\n    translations\n  } = useLanguage();\n  const [stats, setStats] = useState({\n    totalScholarships: 0,\n    totalMessages: 0,\n    totalSubscribers: 0,\n    totalAdmins: 0\n  });\n  const [adminInfo, setAdminInfo] = useState(null);\n  const [isAddAdminModalVisible, setIsAddAdminModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const navigate = useNavigate();\n\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const api = axios.create({\n          baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5001',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          withCredentials: true // Use HTTP-only cookies for authentication\n        });\n\n        // Fetch stats from API\n        const response = await api.get('/api/admin/stats');\n        console.log('API stats response:', response.data);\n        if (response.data) {\n          // Use API data\n          const dashboardStats = {\n            totalScholarships: response.data.totalScholarships || 0,\n            totalMessages: response.data.totalMessages || 0,\n            totalSubscribers: response.data.totalSubscribers || 0,\n            totalAdmins: response.data.totalAdmins || 0\n          };\n          setStats(dashboardStats);\n        } else {\n          throw new Error('Invalid API response');\n        }\n\n        // Get admin info from API\n        try {\n          const adminResponse = await api.get('/api/admin/me');\n          console.log('Admin info response:', adminResponse.data);\n          if (adminResponse.data) {\n            const adminData = adminResponse.data;\n            const currentAdminInfo = {\n              id: adminData.id || 0,\n              email: adminData.email || '',\n              name: adminData.name || 'Admin',\n              role: adminData.role || 'admin',\n              isMainAdmin: adminData.isMainAdmin || false\n            };\n            setAdminInfo(currentAdminInfo);\n          }\n        } catch (adminError) {\n          console.error('Error fetching admin info:', adminError);\n          // If we can't fetch admin info, the user might not be authenticated\n          // The SecureAuthContext will handle redirecting to login if needed\n        }\n\n        // Stats and admin info are already set in the API sections above\n        console.log('Dashboard data fetched:', {\n          stats,\n          adminInfo\n        });\n      } catch (error) {\n        console.error('Error fetching data:', error);\n        message.error('Failed to fetch dashboard data');\n      }\n    };\n    fetchData();\n  }, []);\n  const handleAddAdmin = async values => {\n    try {\n      // Check if current admin is the main admin\n      if (!(adminInfo !== null && adminInfo !== void 0 && adminInfo.isMainAdmin)) {\n        message.error('Only the main admin can create new admins');\n        return;\n      }\n      console.log('Creating new admin with values:', values);\n\n      // Check if we're trying to create a main admin\n      if (values.email.toLowerCase() === '<EMAIL>') {\n        message.error('Cannot create another Main Admin account');\n        return;\n      }\n      const api = axios.create({\n        baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        withCredentials: true // Use HTTP-only cookies for authentication\n      });\n\n      // Create new admin via API\n      const adminData = {\n        name: values.name,\n        email: values.email,\n        role: values.role,\n        password: values.password || 'DefaultPassword123!',\n        // Default password if not provided\n        isMainAdmin: false\n      };\n\n      // Send request to create admin\n      const response = await api.post('/api/admin', adminData);\n      console.log('Admin created response:', response.data);\n\n      // Update stats with incremented admin count\n      setStats(prevStats => ({\n        ...prevStats,\n        totalAdmins: prevStats.totalAdmins + 1\n      }));\n      message.success('Admin created successfully');\n      setIsAddAdminModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      if (error.response && error.response.status === 409) {\n        message.error('An admin with this email already exists');\n      } else {\n        message.error('Failed to create admin');\n        console.error('Error creating admin:', error);\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      className: \"bg-white shadow-sm fixed w-full top-0 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between h-32\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/admin/dashboard\",\n              className: \"flex items-center space-x-4 group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/assets/images/MaBoursedetudeLogo.jpeg\",\n                  alt: translations.brand.name,\n                  className: \"h-20 w-auto rounded-lg shadow-lg transform transition-transform duration-300 group-hover:scale-105\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col space-y-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-4xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent tracking-tight\",\n                  children: translations.brand.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-base text-gray-500 font-medium tracking-wider\",\n                  children: \"Admin Dashboard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pt-32 p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 shadow-lg mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n              children: \"Admin Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mt-2\",\n              children: \"Welcome to your administration portal. Manage scholarships, messages, and more.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/admin/analytics\",\n                className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                children: [/*#__PURE__*/_jsxDEV(AreaChartOutlined, {\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this), \" View Detailed Analytics\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), (adminInfo === null || adminInfo === void 0 ? void 0 : adminInfo.isMainAdmin) && /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 23\n            }, this),\n            onClick: () => setIsAddAdminModalVisible(true),\n            size: \"large\",\n            className: \"bg-gradient-to-r from-blue-600 to-indigo-600 border-none hover:from-blue-700 hover:to-indigo-700\",\n            children: \"Add Admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-white\",\n            children: [/*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Total Scholarships\",\n              value: stats.totalScholarships,\n              prefix: /*#__PURE__*/_jsxDEV(BookOutlined, {\n                className: \"text-blue-500 text-2xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 25\n              }, this),\n              valueStyle: {\n                color: '#3f8600',\n                fontSize: '24px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/admin/scholarships\",\n              className: \"text-blue-600 hover:text-blue-800 mt-4 block font-medium\",\n              children: \"View all scholarships \\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-white\",\n            children: [/*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Messages\",\n              value: stats.totalMessages,\n              prefix: /*#__PURE__*/_jsxDEV(MessageOutlined, {\n                className: \"text-green-500 text-2xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 25\n              }, this),\n              valueStyle: {\n                color: '#1890ff',\n                fontSize: '24px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/admin/messages\",\n              className: \"text-green-600 hover:text-green-800 mt-4 block font-medium\",\n              children: \"View messages \\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-white\",\n            children: [/*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Newsletter Subscribers\",\n              value: stats.totalSubscribers,\n              prefix: /*#__PURE__*/_jsxDEV(MailOutlined, {\n                className: \"text-purple-500 text-2xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 25\n              }, this),\n              valueStyle: {\n                color: '#722ed1',\n                fontSize: '24px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/admin/newsletter\",\n              className: \"text-purple-600 hover:text-purple-800 mt-4 block font-medium\",\n              children: \"Manage subscribers \\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), (adminInfo === null || adminInfo === void 0 ? void 0 : adminInfo.isMainAdmin) && /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-white\",\n            children: [/*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Total Admins\",\n              value: stats.totalAdmins,\n              prefix: /*#__PURE__*/_jsxDEV(TeamOutlined, {\n                className: \"text-cyan-500 text-2xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 27\n              }, this),\n              valueStyle: {\n                color: '#13c2c2',\n                fontSize: '24px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/admin/admins\",\n              className: \"text-cyan-600 hover:text-cyan-800 mt-4 block font-medium\",\n              children: \"Manage admins \\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        title: \"Add New Admin\",\n        open: isAddAdminModalVisible,\n        onCancel: () => setIsAddAdminModalVisible(false),\n        footer: null,\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          form: form,\n          layout: \"vertical\",\n          onFinish: handleAddAdmin,\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"name\",\n            label: \"Name\",\n            rules: [{\n              required: true,\n              message: 'Please input admin name!'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 30\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"email\",\n            label: \"Email\",\n            rules: [{\n              required: true,\n              message: 'Please input admin email!'\n            }, {\n              type: 'email',\n              message: 'Please enter a valid email!'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              prefix: /*#__PURE__*/_jsxDEV(MailOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 30\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"password\",\n            label: \"Password\",\n            rules: [{\n              required: true,\n              message: 'Please input admin password!'\n            }, {\n              min: 8,\n              message: 'Password must be at least 8 characters!'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input.Password, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"role\",\n            label: \"Role\",\n            rules: [{\n              required: true,\n              message: 'Please select admin role!'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n                value: \"admin\",\n                children: \"Admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                value: \"super_admin\",\n                children: \"Super Admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              block: true,\n              children: \"Create Admin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 165,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"G0iGzN0flr2zVc29BZ7tfePDEVo=\", false, function () {\n  return [useLanguage, Form.useForm, useNavigate];\n});\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "axios", "Card", "Statistic", "Row", "Col", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "message", "Layout", "BookOutlined", "MessageOutlined", "UserOutlined", "PlusOutlined", "TeamOutlined", "MailOutlined", "AreaChartOutlined", "useLanguage", "jsxDEV", "_jsxDEV", "Header", "AdminDashboard", "_s", "translations", "stats", "setStats", "totalScholarships", "totalMessages", "totalSubscribers", "totalAdmins", "adminInfo", "setAdminInfo", "isAddAdminModalVisible", "setIsAddAdminModalVisible", "form", "useForm", "navigate", "fetchData", "api", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "headers", "withCredentials", "response", "get", "console", "log", "data", "dashboardStats", "Error", "adminResponse", "adminData", "currentAdminInfo", "id", "email", "name", "role", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "adminError", "error", "handleAddAdmin", "values", "toLowerCase", "password", "post", "prevStats", "success", "resetFields", "status", "className", "children", "to", "src", "alt", "brand", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "icon", "onClick", "size", "gutter", "xs", "sm", "lg", "title", "value", "prefix", "valueStyle", "color", "fontSize", "open", "onCancel", "footer", "layout", "onFinish", "<PERSON><PERSON>", "label", "rules", "required", "min", "Password", "Option", "htmlType", "block", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { Card, Statistic, Row, Col, Button, Modal, Form, Input, Select, message, Layout } from 'antd';\nimport {\n  BookOutlined,\n  MessageOutlined,\n  UserOutlined,\n  PlusOutlined,\n  TeamOutlined,\n  MailOutlined,\n  AreaChartOutlined\n} from '@ant-design/icons';\nimport { useLanguage } from '../../context/LanguageContext';\n\nconst { Header } = Layout;\n\ninterface DashboardStats {\n  totalScholarships: number;\n  totalMessages: number;\n  totalSubscribers: number;\n  totalAdmins: number;\n}\n\ninterface AdminInfo {\n  id: number;\n  email: string;\n  name: string;\n  role: string;\n  isMainAdmin: boolean;\n}\n\nconst AdminDashboard: React.FC = () => {\n  const { translations } = useLanguage();\n  const [stats, setStats] = useState<DashboardStats>({\n    totalScholarships: 0,\n    totalMessages: 0,\n    totalSubscribers: 0,\n    totalAdmins: 0\n  });\n  const [adminInfo, setAdminInfo] = useState<AdminInfo | null>(null);\n  const [isAddAdminModalVisible, setIsAddAdminModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const navigate = useNavigate();\n\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const api = axios.create({\n          baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5001',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          withCredentials: true, // Use HTTP-only cookies for authentication\n        });\n\n        // Fetch stats from API\n        const response = await api.get('/api/admin/stats');\n        console.log('API stats response:', response.data);\n\n        if (response.data) {\n          // Use API data\n          const dashboardStats: DashboardStats = {\n            totalScholarships: response.data.totalScholarships || 0,\n            totalMessages: response.data.totalMessages || 0,\n            totalSubscribers: response.data.totalSubscribers || 0,\n            totalAdmins: response.data.totalAdmins || 0\n          };\n          setStats(dashboardStats);\n        } else {\n          throw new Error('Invalid API response');\n        }\n\n        // Get admin info from API\n        try {\n          const adminResponse = await api.get('/api/admin/me');\n          console.log('Admin info response:', adminResponse.data);\n\n          if (adminResponse.data) {\n            const adminData = adminResponse.data;\n            const currentAdminInfo: AdminInfo = {\n              id: adminData.id || 0,\n              email: adminData.email || '',\n              name: adminData.name || 'Admin',\n              role: adminData.role || 'admin',\n              isMainAdmin: adminData.isMainAdmin || false\n            };\n            setAdminInfo(currentAdminInfo);\n          }\n        } catch (adminError) {\n          console.error('Error fetching admin info:', adminError);\n          // If we can't fetch admin info, the user might not be authenticated\n          // The SecureAuthContext will handle redirecting to login if needed\n        }\n\n        // Stats and admin info are already set in the API sections above\n        console.log('Dashboard data fetched:', { stats, adminInfo });\n      } catch (error) {\n        console.error('Error fetching data:', error);\n        message.error('Failed to fetch dashboard data');\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  const handleAddAdmin = async (values: any) => {\n    try {\n      // Check if current admin is the main admin\n      if (!adminInfo?.isMainAdmin) {\n        message.error('Only the main admin can create new admins');\n        return;\n      }\n\n      console.log('Creating new admin with values:', values);\n\n      // Check if we're trying to create a main admin\n      if (values.email.toLowerCase() === '<EMAIL>') {\n        message.error('Cannot create another Main Admin account');\n        return;\n      }\n\n      const api = axios.create({\n        baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        withCredentials: true, // Use HTTP-only cookies for authentication\n      });\n\n      // Create new admin via API\n      const adminData = {\n        name: values.name,\n        email: values.email,\n        role: values.role,\n        password: values.password || 'DefaultPassword123!', // Default password if not provided\n        isMainAdmin: false\n      };\n\n      // Send request to create admin\n      const response = await api.post('/api/admin', adminData);\n      console.log('Admin created response:', response.data);\n\n      // Update stats with incremented admin count\n      setStats(prevStats => ({\n        ...prevStats,\n        totalAdmins: prevStats.totalAdmins + 1\n      }));\n\n      message.success('Admin created successfully');\n      setIsAddAdminModalVisible(false);\n      form.resetFields();\n    } catch (error: any) {\n      if (error.response && error.response.status === 409) {\n        message.error('An admin with this email already exists');\n      } else {\n        message.error('Failed to create admin');\n        console.error('Error creating admin:', error);\n      }\n    }\n  };\n\n  return (\n    <Layout className=\"min-h-screen bg-gray-50\">\n      <Header className=\"bg-white shadow-sm fixed w-full top-0 z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-32\">\n            <div className=\"flex items-center\">\n              <Link to=\"/admin/dashboard\" className=\"flex items-center space-x-4 group\">\n                <div className=\"relative\">\n                  <img\n                    src=\"/assets/images/MaBoursedetudeLogo.jpeg\"\n                    alt={translations.brand.name}\n                    className=\"h-20 w-auto rounded-lg shadow-lg transform transition-transform duration-300 group-hover:scale-105\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                </div>\n                <div className=\"flex flex-col space-y-1\">\n                  <span className=\"text-4xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent tracking-tight\">\n                    {translations.brand.name}\n                  </span>\n                  <span className=\"text-base text-gray-500 font-medium tracking-wider\">\n                    Admin Dashboard\n                  </span>\n                </div>\n              </Link>\n            </div>\n            <div className=\"flex items-center\">\n              {/* Empty div to maintain the original layout spacing */}\n            </div>\n          </div>\n        </div>\n      </Header>\n\n      <div className=\"pt-32 p-6\">\n        <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 shadow-lg mb-8\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h2 className=\"text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\">\n                Admin Dashboard\n              </h2>\n              <p className=\"text-gray-600 mt-2\">\n                Welcome to your administration portal. Manage scholarships, messages, and more.\n              </p>\n              <div className=\"mt-4\">\n                <Link to=\"/admin/analytics\" className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n                  <AreaChartOutlined className=\"mr-2\" /> View Detailed Analytics\n                </Link>\n              </div>\n            </div>\n            {adminInfo?.isMainAdmin && (\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={() => setIsAddAdminModalVisible(true)}\n                size=\"large\"\n                className=\"bg-gradient-to-r from-blue-600 to-indigo-600 border-none hover:from-blue-700 hover:to-indigo-700\"\n              >\n                Add Admin\n              </Button>\n            )}\n          </div>\n        </div>\n\n        <Row gutter={[16, 16]}>\n          <Col xs={24} sm={12} lg={6}>\n            <Card className=\"hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-white\">\n              <Statistic\n                title=\"Total Scholarships\"\n                value={stats.totalScholarships}\n                prefix={<BookOutlined className=\"text-blue-500 text-2xl\" />}\n                valueStyle={{ color: '#3f8600', fontSize: '24px' }}\n              />\n              <Link to=\"/admin/scholarships\" className=\"text-blue-600 hover:text-blue-800 mt-4 block font-medium\">\n                View all scholarships →\n              </Link>\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} lg={6}>\n            <Card className=\"hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-white\">\n              <Statistic\n                title=\"Messages\"\n                value={stats.totalMessages}\n                prefix={<MessageOutlined className=\"text-green-500 text-2xl\" />}\n                valueStyle={{ color: '#1890ff', fontSize: '24px' }}\n              />\n              <Link to=\"/admin/messages\" className=\"text-green-600 hover:text-green-800 mt-4 block font-medium\">\n                View messages →\n              </Link>\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} lg={6}>\n            <Card className=\"hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-white\">\n              <Statistic\n                title=\"Newsletter Subscribers\"\n                value={stats.totalSubscribers}\n                prefix={<MailOutlined className=\"text-purple-500 text-2xl\" />}\n                valueStyle={{ color: '#722ed1', fontSize: '24px' }}\n              />\n              <Link to=\"/admin/newsletter\" className=\"text-purple-600 hover:text-purple-800 mt-4 block font-medium\">\n                Manage subscribers →\n              </Link>\n            </Card>\n          </Col>\n          {adminInfo?.isMainAdmin && (\n            <Col xs={24} sm={12} lg={6}>\n              <Card className=\"hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-white\">\n                <Statistic\n                  title=\"Total Admins\"\n                  value={stats.totalAdmins}\n                  prefix={<TeamOutlined className=\"text-cyan-500 text-2xl\" />}\n                  valueStyle={{ color: '#13c2c2', fontSize: '24px' }}\n                />\n                <Link to=\"/admin/admins\" className=\"text-cyan-600 hover:text-cyan-800 mt-4 block font-medium\">\n                  Manage admins →\n                </Link>\n              </Card>\n            </Col>\n          )}\n        </Row>\n\n        <Modal\n          title=\"Add New Admin\"\n          open={isAddAdminModalVisible}\n          onCancel={() => setIsAddAdminModalVisible(false)}\n          footer={null}\n        >\n          <Form\n            form={form}\n            layout=\"vertical\"\n            onFinish={handleAddAdmin}\n          >\n            <Form.Item\n              name=\"name\"\n              label=\"Name\"\n              rules={[{ required: true, message: 'Please input admin name!' }]}\n            >\n              <Input prefix={<UserOutlined />} />\n            </Form.Item>\n\n            <Form.Item\n              name=\"email\"\n              label=\"Email\"\n              rules={[\n                { required: true, message: 'Please input admin email!' },\n                { type: 'email', message: 'Please enter a valid email!' }\n              ]}\n            >\n              <Input prefix={<MailOutlined />} />\n            </Form.Item>\n\n            <Form.Item\n              name=\"password\"\n              label=\"Password\"\n              rules={[\n                { required: true, message: 'Please input admin password!' },\n                { min: 8, message: 'Password must be at least 8 characters!' }\n              ]}\n            >\n              <Input.Password />\n            </Form.Item>\n\n            <Form.Item\n              name=\"role\"\n              label=\"Role\"\n              rules={[{ required: true, message: 'Please select admin role!' }]}\n            >\n              <Select>\n                <Select.Option value=\"admin\">Admin</Select.Option>\n                <Select.Option value=\"super_admin\">Super Admin</Select.Option>\n              </Select>\n            </Form.Item>\n\n            <Form.Item>\n              <Button type=\"primary\" htmlType=\"submit\" block>\n                Create Admin\n              </Button>\n            </Form.Item>\n          </Form>\n        </Modal>\n      </div>\n    </Layout>\n  );\n};\n\nexport default AdminDashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,QAAQ,MAAM;AACrG,SACEC,YAAY,EACZC,eAAe,EACfC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,iBAAiB,QACZ,mBAAmB;AAC1B,SAASC,WAAW,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAM;EAAEC;AAAO,CAAC,GAAGX,MAAM;AAiBzB,MAAMY,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM;IAAEC;EAAa,CAAC,GAAGN,WAAW,CAAC,CAAC;EACtC,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAiB;IACjDgC,iBAAiB,EAAE,CAAC;IACpBC,aAAa,EAAE,CAAC;IAChBC,gBAAgB,EAAE,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAmB,IAAI,CAAC;EAClE,MAAM,CAACsC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACwC,IAAI,CAAC,GAAG7B,IAAI,CAAC8B,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAGvC,WAAW,CAAC,CAAC;;EAE9B;EACAF,SAAS,CAAC,MAAM;IACd,MAAM0C,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,MAAMC,GAAG,GAAGxC,KAAK,CAACyC,MAAM,CAAC;UACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;UACjEC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,eAAe,EAAE,IAAI,CAAE;QACzB,CAAC,CAAC;;QAEF;QACA,MAAMC,QAAQ,GAAG,MAAMR,GAAG,CAACS,GAAG,CAAC,kBAAkB,CAAC;QAClDC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEH,QAAQ,CAACI,IAAI,CAAC;QAEjD,IAAIJ,QAAQ,CAACI,IAAI,EAAE;UACjB;UACA,MAAMC,cAA8B,GAAG;YACrCzB,iBAAiB,EAAEoB,QAAQ,CAACI,IAAI,CAACxB,iBAAiB,IAAI,CAAC;YACvDC,aAAa,EAAEmB,QAAQ,CAACI,IAAI,CAACvB,aAAa,IAAI,CAAC;YAC/CC,gBAAgB,EAAEkB,QAAQ,CAACI,IAAI,CAACtB,gBAAgB,IAAI,CAAC;YACrDC,WAAW,EAAEiB,QAAQ,CAACI,IAAI,CAACrB,WAAW,IAAI;UAC5C,CAAC;UACDJ,QAAQ,CAAC0B,cAAc,CAAC;QAC1B,CAAC,MAAM;UACL,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;QACzC;;QAEA;QACA,IAAI;UACF,MAAMC,aAAa,GAAG,MAAMf,GAAG,CAACS,GAAG,CAAC,eAAe,CAAC;UACpDC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEI,aAAa,CAACH,IAAI,CAAC;UAEvD,IAAIG,aAAa,CAACH,IAAI,EAAE;YACtB,MAAMI,SAAS,GAAGD,aAAa,CAACH,IAAI;YACpC,MAAMK,gBAA2B,GAAG;cAClCC,EAAE,EAAEF,SAAS,CAACE,EAAE,IAAI,CAAC;cACrBC,KAAK,EAAEH,SAAS,CAACG,KAAK,IAAI,EAAE;cAC5BC,IAAI,EAAEJ,SAAS,CAACI,IAAI,IAAI,OAAO;cAC/BC,IAAI,EAAEL,SAAS,CAACK,IAAI,IAAI,OAAO;cAC/BC,WAAW,EAAEN,SAAS,CAACM,WAAW,IAAI;YACxC,CAAC;YACD7B,YAAY,CAACwB,gBAAgB,CAAC;UAChC;QACF,CAAC,CAAC,OAAOM,UAAU,EAAE;UACnBb,OAAO,CAACc,KAAK,CAAC,4BAA4B,EAAED,UAAU,CAAC;UACvD;UACA;QACF;;QAEA;QACAb,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE;UAAEzB,KAAK;UAAEM;QAAU,CAAC,CAAC;MAC9D,CAAC,CAAC,OAAOgC,KAAK,EAAE;QACdd,OAAO,CAACc,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5CtD,OAAO,CAACsD,KAAK,CAAC,gCAAgC,CAAC;MACjD;IACF,CAAC;IAEDzB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM0B,cAAc,GAAG,MAAOC,MAAW,IAAK;IAC5C,IAAI;MACF;MACA,IAAI,EAAClC,SAAS,aAATA,SAAS,eAATA,SAAS,CAAE8B,WAAW,GAAE;QAC3BpD,OAAO,CAACsD,KAAK,CAAC,2CAA2C,CAAC;QAC1D;MACF;MAEAd,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEe,MAAM,CAAC;;MAEtD;MACA,IAAIA,MAAM,CAACP,KAAK,CAACQ,WAAW,CAAC,CAAC,KAAK,oBAAoB,EAAE;QACvDzD,OAAO,CAACsD,KAAK,CAAC,0CAA0C,CAAC;QACzD;MACF;MAEA,MAAMxB,GAAG,GAAGxC,KAAK,CAACyC,MAAM,CAAC;QACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;QACjEC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,eAAe,EAAE,IAAI,CAAE;MACzB,CAAC,CAAC;;MAEF;MACA,MAAMS,SAAS,GAAG;QAChBI,IAAI,EAAEM,MAAM,CAACN,IAAI;QACjBD,KAAK,EAAEO,MAAM,CAACP,KAAK;QACnBE,IAAI,EAAEK,MAAM,CAACL,IAAI;QACjBO,QAAQ,EAAEF,MAAM,CAACE,QAAQ,IAAI,qBAAqB;QAAE;QACpDN,WAAW,EAAE;MACf,CAAC;;MAED;MACA,MAAMd,QAAQ,GAAG,MAAMR,GAAG,CAAC6B,IAAI,CAAC,YAAY,EAAEb,SAAS,CAAC;MACxDN,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEH,QAAQ,CAACI,IAAI,CAAC;;MAErD;MACAzB,QAAQ,CAAC2C,SAAS,KAAK;QACrB,GAAGA,SAAS;QACZvC,WAAW,EAAEuC,SAAS,CAACvC,WAAW,GAAG;MACvC,CAAC,CAAC,CAAC;MAEHrB,OAAO,CAAC6D,OAAO,CAAC,4BAA4B,CAAC;MAC7CpC,yBAAyB,CAAC,KAAK,CAAC;MAChCC,IAAI,CAACoC,WAAW,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOR,KAAU,EAAE;MACnB,IAAIA,KAAK,CAAChB,QAAQ,IAAIgB,KAAK,CAAChB,QAAQ,CAACyB,MAAM,KAAK,GAAG,EAAE;QACnD/D,OAAO,CAACsD,KAAK,CAAC,yCAAyC,CAAC;MAC1D,CAAC,MAAM;QACLtD,OAAO,CAACsD,KAAK,CAAC,wBAAwB,CAAC;QACvCd,OAAO,CAACc,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC/C;IACF;EACF,CAAC;EAED,oBACE3C,OAAA,CAACV,MAAM;IAAC+D,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACzCtD,OAAA,CAACC,MAAM;MAACoD,SAAS,EAAC,4CAA4C;MAAAC,QAAA,eAC5DtD,OAAA;QAAKqD,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDtD,OAAA;UAAKqD,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCtD,OAAA;YAAKqD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChCtD,OAAA,CAACvB,IAAI;cAAC8E,EAAE,EAAC,kBAAkB;cAACF,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBACvEtD,OAAA;gBAAKqD,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBtD,OAAA;kBACEwD,GAAG,EAAC,wCAAwC;kBAC5CC,GAAG,EAAErD,YAAY,CAACsD,KAAK,CAACnB,IAAK;kBAC7Bc,SAAS,EAAC;gBAAoG;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/G,CAAC,eACF9D,OAAA;kBAAKqD,SAAS,EAAC;gBAAgJ;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnK,CAAC,eACN9D,OAAA;gBAAKqD,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCtD,OAAA;kBAAMqD,SAAS,EAAC,4GAA4G;kBAAAC,QAAA,EACzHlD,YAAY,CAACsD,KAAK,CAACnB;gBAAI;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACP9D,OAAA;kBAAMqD,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAAC;gBAErE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN9D,OAAA;YAAKqD,SAAS,EAAC;UAAmB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAET9D,OAAA;MAAKqD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBtD,OAAA;QAAKqD,SAAS,EAAC,2EAA2E;QAAAC,QAAA,eACxFtD,OAAA;UAAKqD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDtD,OAAA;YAAAsD,QAAA,gBACEtD,OAAA;cAAIqD,SAAS,EAAC,+FAA+F;cAAAC,QAAA,EAAC;YAE9G;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL9D,OAAA;cAAGqD,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAElC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ9D,OAAA;cAAKqD,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBtD,OAAA,CAACvB,IAAI;gBAAC8E,EAAE,EAAC,kBAAkB;gBAACF,SAAS,EAAC,0GAA0G;gBAAAC,QAAA,gBAC9ItD,OAAA,CAACH,iBAAiB;kBAACwD,SAAS,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BACxC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACL,CAAAnD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE8B,WAAW,kBACrBzC,OAAA,CAAChB,MAAM;YACL+E,IAAI,EAAC,SAAS;YACdC,IAAI,eAAEhE,OAAA,CAACN,YAAY;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBG,OAAO,EAAEA,CAAA,KAAMnD,yBAAyB,CAAC,IAAI,CAAE;YAC/CoD,IAAI,EAAC,OAAO;YACZb,SAAS,EAAC,kGAAkG;YAAAC,QAAA,EAC7G;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9D,OAAA,CAAClB,GAAG;QAACqF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAb,QAAA,gBACpBtD,OAAA,CAACjB,GAAG;UAACqF,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACzBtD,OAAA,CAACpB,IAAI;YAACyE,SAAS,EAAC,8FAA8F;YAAAC,QAAA,gBAC5GtD,OAAA,CAACnB,SAAS;cACR0F,KAAK,EAAC,oBAAoB;cAC1BC,KAAK,EAAEnE,KAAK,CAACE,iBAAkB;cAC/BkE,MAAM,eAAEzE,OAAA,CAACT,YAAY;gBAAC8D,SAAS,EAAC;cAAwB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC5DY,UAAU,EAAE;gBAAEC,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE;cAAO;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACF9D,OAAA,CAACvB,IAAI;cAAC8E,EAAE,EAAC,qBAAqB;cAACF,SAAS,EAAC,0DAA0D;cAAAC,QAAA,EAAC;YAEpG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN9D,OAAA,CAACjB,GAAG;UAACqF,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACzBtD,OAAA,CAACpB,IAAI;YAACyE,SAAS,EAAC,+FAA+F;YAAAC,QAAA,gBAC7GtD,OAAA,CAACnB,SAAS;cACR0F,KAAK,EAAC,UAAU;cAChBC,KAAK,EAAEnE,KAAK,CAACG,aAAc;cAC3BiE,MAAM,eAAEzE,OAAA,CAACR,eAAe;gBAAC6D,SAAS,EAAC;cAAyB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAChEY,UAAU,EAAE;gBAAEC,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE;cAAO;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACF9D,OAAA,CAACvB,IAAI;cAAC8E,EAAE,EAAC,iBAAiB;cAACF,SAAS,EAAC,4DAA4D;cAAAC,QAAA,EAAC;YAElG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN9D,OAAA,CAACjB,GAAG;UAACqF,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACzBtD,OAAA,CAACpB,IAAI;YAACyE,SAAS,EAAC,gGAAgG;YAAAC,QAAA,gBAC9GtD,OAAA,CAACnB,SAAS;cACR0F,KAAK,EAAC,wBAAwB;cAC9BC,KAAK,EAAEnE,KAAK,CAACI,gBAAiB;cAC9BgE,MAAM,eAAEzE,OAAA,CAACJ,YAAY;gBAACyD,SAAS,EAAC;cAA0B;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC9DY,UAAU,EAAE;gBAAEC,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE;cAAO;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACF9D,OAAA,CAACvB,IAAI;cAAC8E,EAAE,EAAC,mBAAmB;cAACF,SAAS,EAAC,8DAA8D;cAAAC,QAAA,EAAC;YAEtG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EACL,CAAAnD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE8B,WAAW,kBACrBzC,OAAA,CAACjB,GAAG;UAACqF,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACzBtD,OAAA,CAACpB,IAAI;YAACyE,SAAS,EAAC,8FAA8F;YAAAC,QAAA,gBAC5GtD,OAAA,CAACnB,SAAS;cACR0F,KAAK,EAAC,cAAc;cACpBC,KAAK,EAAEnE,KAAK,CAACK,WAAY;cACzB+D,MAAM,eAAEzE,OAAA,CAACL,YAAY;gBAAC0D,SAAS,EAAC;cAAwB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC5DY,UAAU,EAAE;gBAAEC,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE;cAAO;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACF9D,OAAA,CAACvB,IAAI;cAAC8E,EAAE,EAAC,eAAe;cAACF,SAAS,EAAC,0DAA0D;cAAAC,QAAA,EAAC;YAE9F;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN9D,OAAA,CAACf,KAAK;QACJsF,KAAK,EAAC,eAAe;QACrBM,IAAI,EAAEhE,sBAAuB;QAC7BiE,QAAQ,EAAEA,CAAA,KAAMhE,yBAAyB,CAAC,KAAK,CAAE;QACjDiE,MAAM,EAAE,IAAK;QAAAzB,QAAA,eAEbtD,OAAA,CAACd,IAAI;UACH6B,IAAI,EAAEA,IAAK;UACXiE,MAAM,EAAC,UAAU;UACjBC,QAAQ,EAAErC,cAAe;UAAAU,QAAA,gBAEzBtD,OAAA,CAACd,IAAI,CAACgG,IAAI;YACR3C,IAAI,EAAC,MAAM;YACX4C,KAAK,EAAC,MAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEhG,OAAO,EAAE;YAA2B,CAAC,CAAE;YAAAiE,QAAA,eAEjEtD,OAAA,CAACb,KAAK;cAACsF,MAAM,eAAEzE,OAAA,CAACP,YAAY;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eAEZ9D,OAAA,CAACd,IAAI,CAACgG,IAAI;YACR3C,IAAI,EAAC,OAAO;YACZ4C,KAAK,EAAC,OAAO;YACbC,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAEhG,OAAO,EAAE;YAA4B,CAAC,EACxD;cAAE0E,IAAI,EAAE,OAAO;cAAE1E,OAAO,EAAE;YAA8B,CAAC,CACzD;YAAAiE,QAAA,eAEFtD,OAAA,CAACb,KAAK;cAACsF,MAAM,eAAEzE,OAAA,CAACJ,YAAY;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eAEZ9D,OAAA,CAACd,IAAI,CAACgG,IAAI;YACR3C,IAAI,EAAC,UAAU;YACf4C,KAAK,EAAC,UAAU;YAChBC,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAEhG,OAAO,EAAE;YAA+B,CAAC,EAC3D;cAAEiG,GAAG,EAAE,CAAC;cAAEjG,OAAO,EAAE;YAA0C,CAAC,CAC9D;YAAAiE,QAAA,eAEFtD,OAAA,CAACb,KAAK,CAACoG,QAAQ;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEZ9D,OAAA,CAACd,IAAI,CAACgG,IAAI;YACR3C,IAAI,EAAC,MAAM;YACX4C,KAAK,EAAC,MAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEhG,OAAO,EAAE;YAA4B,CAAC,CAAE;YAAAiE,QAAA,eAElEtD,OAAA,CAACZ,MAAM;cAAAkE,QAAA,gBACLtD,OAAA,CAACZ,MAAM,CAACoG,MAAM;gBAAChB,KAAK,EAAC,OAAO;gBAAAlB,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC,eAClD9D,OAAA,CAACZ,MAAM,CAACoG,MAAM;gBAAChB,KAAK,EAAC,aAAa;gBAAAlB,QAAA,EAAC;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEZ9D,OAAA,CAACd,IAAI,CAACgG,IAAI;YAAA5B,QAAA,eACRtD,OAAA,CAAChB,MAAM;cAAC+E,IAAI,EAAC,SAAS;cAAC0B,QAAQ,EAAC,QAAQ;cAACC,KAAK;cAAApC,QAAA,EAAC;YAE/C;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAAC3D,EAAA,CAxTID,cAAwB;EAAA,QACHJ,WAAW,EASrBZ,IAAI,CAAC8B,OAAO,EACVtC,WAAW;AAAA;AAAAiH,EAAA,GAXxBzF,cAAwB;AA0T9B,eAAeA,cAAc;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}