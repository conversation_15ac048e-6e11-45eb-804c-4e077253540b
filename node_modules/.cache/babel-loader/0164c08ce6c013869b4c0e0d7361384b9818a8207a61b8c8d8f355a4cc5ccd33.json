{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/NavigationDropdown.tsx\",\n  _s = $RefreshSig$();\n/**\n * Navigation Dropdown Component\n * \n * Specialized dropdown for navigation menus with data fetching,\n * proper routing, and professional styling.\n */\n\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { ChevronDown } from 'lucide-react';\nimport Dropdown from '../common/Dropdown';\nimport { useLanguage } from '../../context/LanguageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NavigationDropdown = ({\n  type,\n  label,\n  className = ''\n}) => {\n  _s();\n  const [items, setItems] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const {\n    translations\n  } = useLanguage();\n\n  // Fetch data when dropdown opens\n  const fetchData = async () => {\n    if (items.length > 0) return; // Don't refetch if we already have data\n\n    setLoading(true);\n    try {\n      let endpoint = '';\n      let dataProcessor = () => [];\n      switch (type) {\n        case 'countries':\n          endpoint = '/api/countries';\n          dataProcessor = countries => [{\n            id: 'all-countries',\n            label: translations.navigation.allCountries || 'All Countries',\n            href: '/pays',\n            count: countries.reduce((sum, c) => sum + c.count, 0)\n          }, ...countries.slice(0, 8).map(country => ({\n            id: country.country.toLowerCase().replace(/\\s+/g, '-'),\n            label: country.country,\n            href: `/pays/${encodeURIComponent(country.country)}`,\n            count: country.count\n          })), ...(countries.length > 8 ? [{\n            id: 'view-all-countries',\n            label: translations.navigation.viewAll || 'View All',\n            href: '/pays'\n          }] : [])];\n          break;\n        case 'scholarships':\n          endpoint = '/api/scholarships/levels';\n          dataProcessor = levels => [{\n            id: 'all-scholarships',\n            label: translations.navigation.allScholarships || 'All Scholarships',\n            href: '/bourses',\n            count: levels.reduce((sum, l) => sum + l.count, 0)\n          }, ...levels.map(level => ({\n            id: level.slug,\n            label: level.name,\n            href: `/${level.name.toLowerCase().replace(/\\s+/g, '-')}`,\n            count: level.openCount\n          }))];\n          break;\n        case 'opportunities':\n          endpoint = '/api/opportunities/types';\n          dataProcessor = types => [{\n            id: 'all-opportunities',\n            label: translations.navigation.allOpportunities || 'All Opportunities',\n            href: '/opportunities',\n            count: types.reduce((sum, t) => sum + t.count, 0)\n          }, ...types.map(opType => ({\n            id: opType.slug,\n            label: opType.name.charAt(0).toUpperCase() + opType.name.slice(1),\n            href: `/opportunities/type/${encodeURIComponent(opType.name)}`,\n            count: opType.activeCount\n          }))];\n          break;\n      }\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${endpoint}`);\n      if (!response.ok) throw new Error('Failed to fetch data');\n      const result = await response.json();\n      const data = result.data || result;\n      setItems(dataProcessor(data));\n    } catch (error) {\n      console.error(`Error fetching ${type} data:`, error);\n      setItems([{\n        id: 'error',\n        label: 'Failed to load data',\n        disabled: true\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Get the main page URL for this navigation type\n  const getMainPageUrl = () => {\n    switch (type) {\n      case 'countries':\n        return '/bourses';\n      case 'scholarships':\n        return '/bourses';\n      case 'opportunities':\n        return '/opportunities';\n      default:\n        return '/';\n    }\n  };\n  const trigger = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n      flex items-center rounded-md text-sm font-medium\n      transition-all duration-200 ease-in-out\n      ${className}\n    `,\n    children: [/*#__PURE__*/_jsxDEV(Link, {\n      to: getMainPageUrl(),\n      className: \"px-3 py-2 text-gray-700 hover:text-primary hover:bg-primary/5 transition-colors duration-200\",\n      onClick: e => e.stopPropagation() // Prevent dropdown from opening when clicking the link\n      ,\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"transition-colors duration-200\",\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-1 py-2 text-gray-400 hover:text-primary transition-colors duration-200 cursor-pointer\",\n      onClick: e => {\n        e.stopPropagation();\n        // This will be handled by the dropdown's click handler\n      },\n      children: /*#__PURE__*/_jsxDEV(ChevronDown, {\n        size: 14,\n        className: \"transition-all duration-200 ease-out group-hover:rotate-180\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"group relative\",\n    children: /*#__PURE__*/_jsxDEV(Dropdown, {\n      trigger: trigger,\n      items: items,\n      loading: loading,\n      onOpen: fetchData,\n      showOnHover: true,\n      closeOnClick: true,\n      placement: \"bottom-left\",\n      className: \"\",\n      dropdownClassName: \"border-t-2 border-primary\",\n      emptyMessage: `No ${type} available`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 183,\n    columnNumber: 5\n  }, this);\n};\n_s(NavigationDropdown, \"dSYAOEQJpmUEaAadZBeAZJtT6G0=\", false, function () {\n  return [useLanguage];\n});\n_c = NavigationDropdown;\nexport default NavigationDropdown;\nvar _c;\n$RefreshReg$(_c, \"NavigationDropdown\");", "map": {"version": 3, "names": ["React", "useState", "Link", "ChevronDown", "Dropdown", "useLanguage", "jsxDEV", "_jsxDEV", "NavigationDropdown", "type", "label", "className", "_s", "items", "setItems", "loading", "setLoading", "translations", "fetchData", "length", "endpoint", "dataProcessor", "countries", "id", "navigation", "allCountries", "href", "count", "reduce", "sum", "c", "slice", "map", "country", "toLowerCase", "replace", "encodeURIComponent", "viewAll", "levels", "allScholarships", "l", "level", "slug", "name", "openCount", "types", "allOpportunities", "t", "opType", "char<PERSON>t", "toUpperCase", "activeCount", "response", "fetch", "process", "env", "REACT_APP_API_URL", "ok", "Error", "result", "json", "data", "error", "console", "disabled", "getMainPageUrl", "trigger", "children", "to", "onClick", "e", "stopPropagation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onOpen", "showOnHover", "closeOnClick", "placement", "dropdownClassName", "emptyMessage", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/NavigationDropdown.tsx"], "sourcesContent": ["/**\n * Navigation Dropdown Component\n * \n * Specialized dropdown for navigation menus with data fetching,\n * proper routing, and professional styling.\n */\n\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { ChevronDown } from 'lucide-react';\nimport Dropdown, { DropdownItem } from '../common/Dropdown';\nimport { useLanguage } from '../../context/LanguageContext';\n\ninterface NavigationDropdownProps {\n  type: 'countries' | 'scholarships' | 'opportunities';\n  label: string;\n  className?: string;\n}\n\ninterface CountryData {\n  country: string;\n  count: number;\n}\n\ninterface LevelData {\n  name: string;\n  count: number;\n  openCount: number;\n  slug: string;\n}\n\ninterface OpportunityTypeData {\n  name: string;\n  count: number;\n  activeCount: number;\n  slug: string;\n}\n\nconst NavigationDropdown: React.FC<NavigationDropdownProps> = ({\n  type,\n  label,\n  className = ''\n}) => {\n  const [items, setItems] = useState<DropdownItem[]>([]);\n  const [loading, setLoading] = useState(false);\n  const { translations } = useLanguage();\n\n  // Fetch data when dropdown opens\n  const fetchData = async () => {\n    if (items.length > 0) return; // Don't refetch if we already have data\n    \n    setLoading(true);\n    try {\n      let endpoint = '';\n      let dataProcessor: (data: any[]) => DropdownItem[] = () => [];\n\n      switch (type) {\n        case 'countries':\n          endpoint = '/api/countries';\n          dataProcessor = (countries: CountryData[]) => [\n            {\n              id: 'all-countries',\n              label: translations.navigation.allCountries || 'All Countries',\n              href: '/pays',\n              count: countries.reduce((sum, c) => sum + c.count, 0)\n            },\n            ...countries.slice(0, 8).map(country => ({\n              id: country.country.toLowerCase().replace(/\\s+/g, '-'),\n              label: country.country,\n              href: `/pays/${encodeURIComponent(country.country)}`,\n              count: country.count\n            })),\n            ...(countries.length > 8 ? [{\n              id: 'view-all-countries',\n              label: translations.navigation.viewAll || 'View All',\n              href: '/pays'\n            }] : [])\n          ];\n          break;\n\n        case 'scholarships':\n          endpoint = '/api/scholarships/levels';\n          dataProcessor = (levels: LevelData[]) => [\n            {\n              id: 'all-scholarships',\n              label: translations.navigation.allScholarships || 'All Scholarships',\n              href: '/bourses',\n              count: levels.reduce((sum, l) => sum + l.count, 0)\n            },\n            ...levels.map(level => ({\n              id: level.slug,\n              label: level.name,\n              href: `/${level.name.toLowerCase().replace(/\\s+/g, '-')}`,\n              count: level.openCount\n            }))\n          ];\n          break;\n\n        case 'opportunities':\n          endpoint = '/api/opportunities/types';\n          dataProcessor = (types: OpportunityTypeData[]) => [\n            {\n              id: 'all-opportunities',\n              label: translations.navigation.allOpportunities || 'All Opportunities',\n              href: '/opportunities',\n              count: types.reduce((sum, t) => sum + t.count, 0)\n            },\n            ...types.map(opType => ({\n              id: opType.slug,\n              label: opType.name.charAt(0).toUpperCase() + opType.name.slice(1),\n              href: `/opportunities/type/${encodeURIComponent(opType.name)}`,\n              count: opType.activeCount\n            }))\n          ];\n          break;\n      }\n\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${endpoint}`);\n      if (!response.ok) throw new Error('Failed to fetch data');\n      \n      const result = await response.json();\n      const data = result.data || result;\n      \n      setItems(dataProcessor(data));\n    } catch (error) {\n      console.error(`Error fetching ${type} data:`, error);\n      setItems([{\n        id: 'error',\n        label: 'Failed to load data',\n        disabled: true\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Get the main page URL for this navigation type\n  const getMainPageUrl = () => {\n    switch (type) {\n      case 'countries':\n        return '/bourses';\n      case 'scholarships':\n        return '/bourses';\n      case 'opportunities':\n        return '/opportunities';\n      default:\n        return '/';\n    }\n  };\n\n  const trigger = (\n    <div className={`\n      flex items-center rounded-md text-sm font-medium\n      transition-all duration-200 ease-in-out\n      ${className}\n    `}>\n      {/* Main clickable link */}\n      <Link\n        to={getMainPageUrl()}\n        className=\"px-3 py-2 text-gray-700 hover:text-primary hover:bg-primary/5 transition-colors duration-200\"\n        onClick={(e) => e.stopPropagation()} // Prevent dropdown from opening when clicking the link\n      >\n        <span className=\"transition-colors duration-200\">{label}</span>\n      </Link>\n\n      {/* Dropdown arrow - separate clickable area */}\n      <div\n        className=\"px-1 py-2 text-gray-400 hover:text-primary transition-colors duration-200 cursor-pointer\"\n        onClick={(e) => {\n          e.stopPropagation();\n          // This will be handled by the dropdown's click handler\n        }}\n      >\n        <ChevronDown\n          size={14}\n          className=\"transition-all duration-200 ease-out group-hover:rotate-180\"\n        />\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"group relative\">\n      <Dropdown\n        trigger={trigger}\n        items={items}\n        loading={loading}\n        onOpen={fetchData}\n        showOnHover={true}\n        closeOnClick={true}\n        placement=\"bottom-left\"\n        className=\"\"\n        dropdownClassName=\"border-t-2 border-primary\"\n        emptyMessage={`No ${type} available`}\n      />\n    </div>\n  );\n};\n\nexport default NavigationDropdown;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,WAAW,QAAQ,cAAc;AAC1C,OAAOC,QAAQ,MAAwB,oBAAoB;AAC3D,SAASC,WAAW,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA2B5D,MAAMC,kBAAqD,GAAGA,CAAC;EAC7DC,IAAI;EACJC,KAAK;EACLC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAiB,EAAE,CAAC;EACtD,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEgB;EAAa,CAAC,GAAGZ,WAAW,CAAC,CAAC;;EAEtC;EACA,MAAMa,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAIL,KAAK,CAACM,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC;;IAE9BH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,IAAII,QAAQ,GAAG,EAAE;MACjB,IAAIC,aAA8C,GAAGA,CAAA,KAAM,EAAE;MAE7D,QAAQZ,IAAI;QACV,KAAK,WAAW;UACdW,QAAQ,GAAG,gBAAgB;UAC3BC,aAAa,GAAIC,SAAwB,IAAK,CAC5C;YACEC,EAAE,EAAE,eAAe;YACnBb,KAAK,EAAEO,YAAY,CAACO,UAAU,CAACC,YAAY,IAAI,eAAe;YAC9DC,IAAI,EAAE,OAAO;YACbC,KAAK,EAAEL,SAAS,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACH,KAAK,EAAE,CAAC;UACtD,CAAC,EACD,GAAGL,SAAS,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACC,OAAO,KAAK;YACvCV,EAAE,EAAEU,OAAO,CAACA,OAAO,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;YACtDzB,KAAK,EAAEuB,OAAO,CAACA,OAAO;YACtBP,IAAI,EAAE,SAASU,kBAAkB,CAACH,OAAO,CAACA,OAAO,CAAC,EAAE;YACpDN,KAAK,EAAEM,OAAO,CAACN;UACjB,CAAC,CAAC,CAAC,EACH,IAAIL,SAAS,CAACH,MAAM,GAAG,CAAC,GAAG,CAAC;YAC1BI,EAAE,EAAE,oBAAoB;YACxBb,KAAK,EAAEO,YAAY,CAACO,UAAU,CAACa,OAAO,IAAI,UAAU;YACpDX,IAAI,EAAE;UACR,CAAC,CAAC,GAAG,EAAE,CAAC,CACT;UACD;QAEF,KAAK,cAAc;UACjBN,QAAQ,GAAG,0BAA0B;UACrCC,aAAa,GAAIiB,MAAmB,IAAK,CACvC;YACEf,EAAE,EAAE,kBAAkB;YACtBb,KAAK,EAAEO,YAAY,CAACO,UAAU,CAACe,eAAe,IAAI,kBAAkB;YACpEb,IAAI,EAAE,UAAU;YAChBC,KAAK,EAAEW,MAAM,CAACV,MAAM,CAAC,CAACC,GAAG,EAAEW,CAAC,KAAKX,GAAG,GAAGW,CAAC,CAACb,KAAK,EAAE,CAAC;UACnD,CAAC,EACD,GAAGW,MAAM,CAACN,GAAG,CAACS,KAAK,KAAK;YACtBlB,EAAE,EAAEkB,KAAK,CAACC,IAAI;YACdhC,KAAK,EAAE+B,KAAK,CAACE,IAAI;YACjBjB,IAAI,EAAE,IAAIe,KAAK,CAACE,IAAI,CAACT,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;YACzDR,KAAK,EAAEc,KAAK,CAACG;UACf,CAAC,CAAC,CAAC,CACJ;UACD;QAEF,KAAK,eAAe;UAClBxB,QAAQ,GAAG,0BAA0B;UACrCC,aAAa,GAAIwB,KAA4B,IAAK,CAChD;YACEtB,EAAE,EAAE,mBAAmB;YACvBb,KAAK,EAAEO,YAAY,CAACO,UAAU,CAACsB,gBAAgB,IAAI,mBAAmB;YACtEpB,IAAI,EAAE,gBAAgB;YACtBC,KAAK,EAAEkB,KAAK,CAACjB,MAAM,CAAC,CAACC,GAAG,EAAEkB,CAAC,KAAKlB,GAAG,GAAGkB,CAAC,CAACpB,KAAK,EAAE,CAAC;UAClD,CAAC,EACD,GAAGkB,KAAK,CAACb,GAAG,CAACgB,MAAM,KAAK;YACtBzB,EAAE,EAAEyB,MAAM,CAACN,IAAI;YACfhC,KAAK,EAAEsC,MAAM,CAACL,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,MAAM,CAACL,IAAI,CAACZ,KAAK,CAAC,CAAC,CAAC;YACjEL,IAAI,EAAE,uBAAuBU,kBAAkB,CAACY,MAAM,CAACL,IAAI,CAAC,EAAE;YAC9DhB,KAAK,EAAEqB,MAAM,CAACG;UAChB,CAAC,CAAC,CAAC,CACJ;UACD;MACJ;MAEA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,GAAGpC,QAAQ,EAAE,CAAC;MACtG,IAAI,CAACgC,QAAQ,CAACK,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;MAEzD,MAAMC,MAAM,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MACpC,MAAMC,IAAI,GAAGF,MAAM,CAACE,IAAI,IAAIF,MAAM;MAElC7C,QAAQ,CAACO,aAAa,CAACwC,IAAI,CAAC,CAAC;IAC/B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kBAAkBrD,IAAI,QAAQ,EAAEqD,KAAK,CAAC;MACpDhD,QAAQ,CAAC,CAAC;QACRS,EAAE,EAAE,OAAO;QACXb,KAAK,EAAE,qBAAqB;QAC5BsD,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRhD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMiD,cAAc,GAAGA,CAAA,KAAM;IAC3B,QAAQxD,IAAI;MACV,KAAK,WAAW;QACd,OAAO,UAAU;MACnB,KAAK,cAAc;QACjB,OAAO,UAAU;MACnB,KAAK,eAAe;QAClB,OAAO,gBAAgB;MACzB;QACE,OAAO,GAAG;IACd;EACF,CAAC;EAED,MAAMyD,OAAO,gBACX3D,OAAA;IAAKI,SAAS,EAAE;AACpB;AACA;AACA,QAAQA,SAAS;AACjB,KAAM;IAAAwD,QAAA,gBAEA5D,OAAA,CAACL,IAAI;MACHkE,EAAE,EAAEH,cAAc,CAAC,CAAE;MACrBtD,SAAS,EAAC,8FAA8F;MACxG0D,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE,CAAC;MAAA;MAAAJ,QAAA,eAErC5D,OAAA;QAAMI,SAAS,EAAC,gCAAgC;QAAAwD,QAAA,EAAEzD;MAAK;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC,eAGPpE,OAAA;MACEI,SAAS,EAAC,0FAA0F;MACpG0D,OAAO,EAAGC,CAAC,IAAK;QACdA,CAAC,CAACC,eAAe,CAAC,CAAC;QACnB;MACF,CAAE;MAAAJ,QAAA,eAEF5D,OAAA,CAACJ,WAAW;QACVyE,IAAI,EAAE,EAAG;QACTjE,SAAS,EAAC;MAA6D;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACEpE,OAAA;IAAKI,SAAS,EAAC,gBAAgB;IAAAwD,QAAA,eAC7B5D,OAAA,CAACH,QAAQ;MACP8D,OAAO,EAAEA,OAAQ;MACjBrD,KAAK,EAAEA,KAAM;MACbE,OAAO,EAAEA,OAAQ;MACjB8D,MAAM,EAAE3D,SAAU;MAClB4D,WAAW,EAAE,IAAK;MAClBC,YAAY,EAAE,IAAK;MACnBC,SAAS,EAAC,aAAa;MACvBrE,SAAS,EAAC,EAAE;MACZsE,iBAAiB,EAAC,2BAA2B;MAC7CC,YAAY,EAAE,MAAMzE,IAAI;IAAa;MAAA+D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC/D,EAAA,CA/JIJ,kBAAqD;EAAA,QAOhCH,WAAW;AAAA;AAAA8E,EAAA,GAPhC3E,kBAAqD;AAiK3D,eAAeA,kBAAkB;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}