{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/MobileNavigationDropdown.tsx\",\n  _s = $RefreshSig$();\n/**\n * Mobile Navigation Dropdown Component\n * \n * Touch-friendly accordion-style dropdown for mobile navigation\n * with smooth animations and proper accessibility.\n */\n\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { ChevronDown } from 'lucide-react';\nimport { useLanguage } from '../../context/LanguageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MobileNavigationDropdown = ({\n  type,\n  label,\n  onItemClick\n}) => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [items, setItems] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const {\n    translations\n  } = useLanguage();\n  const fetchData = async () => {\n    if (items.length > 0) return;\n    setLoading(true);\n    try {\n      let endpoint = '';\n      let dataProcessor = () => [];\n      switch (type) {\n        case 'countries':\n          endpoint = '/api/countries';\n          dataProcessor = countries => [{\n            id: 'all-countries',\n            label: translations.navigation.allCountries || 'All Countries',\n            href: '/pays',\n            count: countries.reduce((sum, c) => sum + c.count, 0)\n          }, ...countries.slice(0, 6).map(country => ({\n            id: country.country.toLowerCase().replace(/\\s+/g, '-'),\n            label: country.country,\n            href: `/pays/${encodeURIComponent(country.country)}`,\n            count: country.count\n          }))];\n          break;\n        case 'scholarships':\n          endpoint = '/api/scholarships/levels';\n          dataProcessor = levels => [{\n            id: 'all-scholarships',\n            label: translations.navigation.allScholarships || 'All Scholarships',\n            href: '/bourses',\n            count: levels.reduce((sum, l) => sum + l.count, 0)\n          }, ...levels.map(level => ({\n            id: level.slug,\n            label: level.name,\n            href: `/bourses?level=${encodeURIComponent(level.name)}`,\n            count: level.openCount\n          }))];\n          break;\n        case 'opportunities':\n          endpoint = '/api/opportunities/types';\n          dataProcessor = types => [{\n            id: 'all-opportunities',\n            label: translations.navigation.allOpportunities || 'All Opportunities',\n            href: '/opportunities',\n            count: types.reduce((sum, t) => sum + t.count, 0)\n          }, ...types.map(opType => ({\n            id: opType.slug,\n            label: opType.name.charAt(0).toUpperCase() + opType.name.slice(1),\n            href: `/opportunities/type/${encodeURIComponent(opType.name)}`,\n            count: opType.activeCount\n          }))];\n          break;\n      }\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${endpoint}`);\n      if (!response.ok) throw new Error('Failed to fetch data');\n      const result = await response.json();\n      const data = result.data || result;\n      setItems(dataProcessor(data));\n    } catch (error) {\n      console.error(`Error fetching ${type} data:`, error);\n      setItems([{\n        id: 'error',\n        label: 'Failed to load data',\n        href: `/${type}`\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleToggle = () => {\n    if (!isOpen && items.length === 0) {\n      fetchData();\n    }\n    setIsOpen(!isOpen);\n  };\n\n  // Get the main page URL for this navigation type\n  const getMainPageUrl = () => {\n    switch (type) {\n      case 'countries':\n        return '/countries';\n      case 'scholarships':\n        return '/scholarships';\n      case 'opportunities':\n        return '/opportunities';\n      default:\n        return '/';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: getMainPageUrl(),\n        onClick: onItemClick,\n        className: \"flex-1 px-4 py-3 text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50 transition-colors duration-200\",\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleToggle,\n        className: \"px-4 py-3 text-gray-400 hover:text-primary transition-colors duration-200\",\n        \"aria-expanded\": isOpen,\n        \"aria-controls\": `mobile-dropdown-${type}`,\n        \"aria-label\": `Toggle ${label} submenu`,\n        children: /*#__PURE__*/_jsxDEV(ChevronDown, {\n          size: 18,\n          className: `transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      id: `mobile-dropdown-${type}`,\n      className: `\n          overflow-hidden transition-all duration-300 ease-in-out\n          ${isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}\n        `,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50\",\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-primary mx-auto mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this) : items.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 text-center text-gray-500 text-sm\",\n          children: \"No items available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-1\",\n          children: items.map(item => /*#__PURE__*/_jsxDEV(Link, {\n            to: item.href,\n            onClick: onItemClick,\n            className: \"flex items-center justify-between px-8 py-2 text-sm text-gray-600 hover:text-primary hover:bg-white transition-colors duration-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: item.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 19\n            }, this), item.count !== undefined && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full\",\n              children: item.count\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 21\n            }, this)]\n          }, item.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 155,\n    columnNumber: 5\n  }, this);\n};\n_s(MobileNavigationDropdown, \"cB8is+q46C6RbO8OO77S0MTBqWQ=\", false, function () {\n  return [useLanguage];\n});\n_c = MobileNavigationDropdown;\nexport default MobileNavigationDropdown;\nvar _c;\n$RefreshReg$(_c, \"MobileNavigationDropdown\");", "map": {"version": 3, "names": ["React", "useState", "Link", "ChevronDown", "useLanguage", "jsxDEV", "_jsxDEV", "MobileNavigationDropdown", "type", "label", "onItemClick", "_s", "isOpen", "setIsOpen", "items", "setItems", "loading", "setLoading", "translations", "fetchData", "length", "endpoint", "dataProcessor", "countries", "id", "navigation", "allCountries", "href", "count", "reduce", "sum", "c", "slice", "map", "country", "toLowerCase", "replace", "encodeURIComponent", "levels", "allScholarships", "l", "level", "slug", "name", "openCount", "types", "allOpportunities", "t", "opType", "char<PERSON>t", "toUpperCase", "activeCount", "response", "fetch", "process", "env", "REACT_APP_API_URL", "ok", "Error", "result", "json", "data", "error", "console", "handleToggle", "getMainPageUrl", "children", "className", "to", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "item", "undefined", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/MobileNavigationDropdown.tsx"], "sourcesContent": ["/**\n * Mobile Navigation Dropdown Component\n * \n * Touch-friendly accordion-style dropdown for mobile navigation\n * with smooth animations and proper accessibility.\n */\n\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { ChevronDown } from 'lucide-react';\nimport { useLanguage } from '../../context/LanguageContext';\n\ninterface MobileNavigationDropdownProps {\n  type: 'countries' | 'scholarships' | 'opportunities';\n  label: string;\n  onItemClick?: () => void;\n}\n\ninterface CountryData {\n  country: string;\n  count: number;\n}\n\ninterface LevelData {\n  name: string;\n  count: number;\n  openCount: number;\n  slug: string;\n}\n\ninterface OpportunityTypeData {\n  name: string;\n  count: number;\n  activeCount: number;\n  slug: string;\n}\n\nconst MobileNavigationDropdown: React.FC<MobileNavigationDropdownProps> = ({\n  type,\n  label,\n  onItemClick\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [items, setItems] = useState<any[]>([]);\n  const [loading, setLoading] = useState(false);\n  const { translations } = useLanguage();\n\n  const fetchData = async () => {\n    if (items.length > 0) return;\n    \n    setLoading(true);\n    try {\n      let endpoint = '';\n      let dataProcessor: (data: any[]) => any[] = () => [];\n\n      switch (type) {\n        case 'countries':\n          endpoint = '/api/countries';\n          dataProcessor = (countries: CountryData[]) => [\n            {\n              id: 'all-countries',\n              label: translations.navigation.allCountries || 'All Countries',\n              href: '/pays',\n              count: countries.reduce((sum, c) => sum + c.count, 0)\n            },\n            ...countries.slice(0, 6).map(country => ({\n              id: country.country.toLowerCase().replace(/\\s+/g, '-'),\n              label: country.country,\n              href: `/pays/${encodeURIComponent(country.country)}`,\n              count: country.count\n            }))\n          ];\n          break;\n\n        case 'scholarships':\n          endpoint = '/api/scholarships/levels';\n          dataProcessor = (levels: LevelData[]) => [\n            {\n              id: 'all-scholarships',\n              label: translations.navigation.allScholarships || 'All Scholarships',\n              href: '/bourses',\n              count: levels.reduce((sum, l) => sum + l.count, 0)\n            },\n            ...levels.map(level => ({\n              id: level.slug,\n              label: level.name,\n              href: `/bourses?level=${encodeURIComponent(level.name)}`,\n              count: level.openCount\n            }))\n          ];\n          break;\n\n        case 'opportunities':\n          endpoint = '/api/opportunities/types';\n          dataProcessor = (types: OpportunityTypeData[]) => [\n            {\n              id: 'all-opportunities',\n              label: translations.navigation.allOpportunities || 'All Opportunities',\n              href: '/opportunities',\n              count: types.reduce((sum, t) => sum + t.count, 0)\n            },\n            ...types.map(opType => ({\n              id: opType.slug,\n              label: opType.name.charAt(0).toUpperCase() + opType.name.slice(1),\n              href: `/opportunities/type/${encodeURIComponent(opType.name)}`,\n              count: opType.activeCount\n            }))\n          ];\n          break;\n      }\n\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${endpoint}`);\n      if (!response.ok) throw new Error('Failed to fetch data');\n      \n      const result = await response.json();\n      const data = result.data || result;\n      \n      setItems(dataProcessor(data));\n    } catch (error) {\n      console.error(`Error fetching ${type} data:`, error);\n      setItems([{\n        id: 'error',\n        label: 'Failed to load data',\n        href: `/${type}`\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleToggle = () => {\n    if (!isOpen && items.length === 0) {\n      fetchData();\n    }\n    setIsOpen(!isOpen);\n  };\n\n\n\n  // Get the main page URL for this navigation type\n  const getMainPageUrl = () => {\n    switch (type) {\n      case 'countries':\n        return '/countries';\n      case 'scholarships':\n        return '/scholarships';\n      case 'opportunities':\n        return '/opportunities';\n      default:\n        return '/';\n    }\n  };\n\n  return (\n    <div>\n      {/* Main Navigation Item with Link and Toggle */}\n      <div className=\"flex items-center\">\n        {/* Main clickable link */}\n        <Link\n          to={getMainPageUrl()}\n          onClick={onItemClick}\n          className=\"flex-1 px-4 py-3 text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50 transition-colors duration-200\"\n        >\n          {label}\n        </Link>\n\n        {/* Dropdown toggle button */}\n        <button\n          onClick={handleToggle}\n          className=\"px-4 py-3 text-gray-400 hover:text-primary transition-colors duration-200\"\n          aria-expanded={isOpen}\n          aria-controls={`mobile-dropdown-${type}`}\n          aria-label={`Toggle ${label} submenu`}\n        >\n          <ChevronDown\n            size={18}\n            className={`transition-transform duration-200 ${\n              isOpen ? 'rotate-180' : ''\n            }`}\n          />\n        </button>\n      </div>\n\n      {/* Dropdown Content */}\n      <div\n        id={`mobile-dropdown-${type}`}\n        className={`\n          overflow-hidden transition-all duration-300 ease-in-out\n          ${isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}\n        `}\n      >\n        <div className=\"bg-gray-50\">\n          {loading ? (\n            <div className=\"px-6 py-4 text-center\">\n              <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-primary mx-auto mb-2\"></div>\n              <span className=\"text-sm text-gray-500\">Loading...</span>\n            </div>\n          ) : items.length === 0 ? (\n            <div className=\"px-6 py-4 text-center text-gray-500 text-sm\">\n              No items available\n            </div>\n          ) : (\n            <div className=\"py-1\">\n              {items.map((item) => (\n                <Link\n                  key={item.id}\n                  to={item.href}\n                  onClick={onItemClick}\n                  className=\"flex items-center justify-between px-8 py-2 text-sm text-gray-600 hover:text-primary hover:bg-white transition-colors duration-200\"\n                >\n                  <span>{item.label}</span>\n                  {item.count !== undefined && (\n                    <span className=\"text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full\">\n                      {item.count}\n                    </span>\n                  )}\n                </Link>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MobileNavigationDropdown;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,WAAW,QAAQ,cAAc;AAC1C,SAASC,WAAW,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA2B5D,MAAMC,wBAAiE,GAAGA,CAAC;EACzEC,IAAI;EACJC,KAAK;EACLC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAQ,EAAE,CAAC;EAC7C,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEiB;EAAa,CAAC,GAAGd,WAAW,CAAC,CAAC;EAEtC,MAAMe,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAIL,KAAK,CAACM,MAAM,GAAG,CAAC,EAAE;IAEtBH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,IAAII,QAAQ,GAAG,EAAE;MACjB,IAAIC,aAAqC,GAAGA,CAAA,KAAM,EAAE;MAEpD,QAAQd,IAAI;QACV,KAAK,WAAW;UACda,QAAQ,GAAG,gBAAgB;UAC3BC,aAAa,GAAIC,SAAwB,IAAK,CAC5C;YACEC,EAAE,EAAE,eAAe;YACnBf,KAAK,EAAES,YAAY,CAACO,UAAU,CAACC,YAAY,IAAI,eAAe;YAC9DC,IAAI,EAAE,OAAO;YACbC,KAAK,EAAEL,SAAS,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACH,KAAK,EAAE,CAAC;UACtD,CAAC,EACD,GAAGL,SAAS,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACC,OAAO,KAAK;YACvCV,EAAE,EAAEU,OAAO,CAACA,OAAO,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;YACtD3B,KAAK,EAAEyB,OAAO,CAACA,OAAO;YACtBP,IAAI,EAAE,SAASU,kBAAkB,CAACH,OAAO,CAACA,OAAO,CAAC,EAAE;YACpDN,KAAK,EAAEM,OAAO,CAACN;UACjB,CAAC,CAAC,CAAC,CACJ;UACD;QAEF,KAAK,cAAc;UACjBP,QAAQ,GAAG,0BAA0B;UACrCC,aAAa,GAAIgB,MAAmB,IAAK,CACvC;YACEd,EAAE,EAAE,kBAAkB;YACtBf,KAAK,EAAES,YAAY,CAACO,UAAU,CAACc,eAAe,IAAI,kBAAkB;YACpEZ,IAAI,EAAE,UAAU;YAChBC,KAAK,EAAEU,MAAM,CAACT,MAAM,CAAC,CAACC,GAAG,EAAEU,CAAC,KAAKV,GAAG,GAAGU,CAAC,CAACZ,KAAK,EAAE,CAAC;UACnD,CAAC,EACD,GAAGU,MAAM,CAACL,GAAG,CAACQ,KAAK,KAAK;YACtBjB,EAAE,EAAEiB,KAAK,CAACC,IAAI;YACdjC,KAAK,EAAEgC,KAAK,CAACE,IAAI;YACjBhB,IAAI,EAAE,kBAAkBU,kBAAkB,CAACI,KAAK,CAACE,IAAI,CAAC,EAAE;YACxDf,KAAK,EAAEa,KAAK,CAACG;UACf,CAAC,CAAC,CAAC,CACJ;UACD;QAEF,KAAK,eAAe;UAClBvB,QAAQ,GAAG,0BAA0B;UACrCC,aAAa,GAAIuB,KAA4B,IAAK,CAChD;YACErB,EAAE,EAAE,mBAAmB;YACvBf,KAAK,EAAES,YAAY,CAACO,UAAU,CAACqB,gBAAgB,IAAI,mBAAmB;YACtEnB,IAAI,EAAE,gBAAgB;YACtBC,KAAK,EAAEiB,KAAK,CAAChB,MAAM,CAAC,CAACC,GAAG,EAAEiB,CAAC,KAAKjB,GAAG,GAAGiB,CAAC,CAACnB,KAAK,EAAE,CAAC;UAClD,CAAC,EACD,GAAGiB,KAAK,CAACZ,GAAG,CAACe,MAAM,KAAK;YACtBxB,EAAE,EAAEwB,MAAM,CAACN,IAAI;YACfjC,KAAK,EAAEuC,MAAM,CAACL,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,MAAM,CAACL,IAAI,CAACX,KAAK,CAAC,CAAC,CAAC;YACjEL,IAAI,EAAE,uBAAuBU,kBAAkB,CAACW,MAAM,CAACL,IAAI,CAAC,EAAE;YAC9Df,KAAK,EAAEoB,MAAM,CAACG;UAChB,CAAC,CAAC,CAAC,CACJ;UACD;MACJ;MAEA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,GAAGnC,QAAQ,EAAE,CAAC;MACtG,IAAI,CAAC+B,QAAQ,CAACK,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;MAEzD,MAAMC,MAAM,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MACpC,MAAMC,IAAI,GAAGF,MAAM,CAACE,IAAI,IAAIF,MAAM;MAElC5C,QAAQ,CAACO,aAAa,CAACuC,IAAI,CAAC,CAAC;IAC/B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kBAAkBtD,IAAI,QAAQ,EAAEsD,KAAK,CAAC;MACpD/C,QAAQ,CAAC,CAAC;QACRS,EAAE,EAAE,OAAO;QACXf,KAAK,EAAE,qBAAqB;QAC5BkB,IAAI,EAAE,IAAInB,IAAI;MAChB,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRS,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+C,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACpD,MAAM,IAAIE,KAAK,CAACM,MAAM,KAAK,CAAC,EAAE;MACjCD,SAAS,CAAC,CAAC;IACb;IACAN,SAAS,CAAC,CAACD,MAAM,CAAC;EACpB,CAAC;;EAID;EACA,MAAMqD,cAAc,GAAGA,CAAA,KAAM;IAC3B,QAAQzD,IAAI;MACV,KAAK,WAAW;QACd,OAAO,YAAY;MACrB,KAAK,cAAc;QACjB,OAAO,eAAe;MACxB,KAAK,eAAe;QAClB,OAAO,gBAAgB;MACzB;QACE,OAAO,GAAG;IACd;EACF,CAAC;EAED,oBACEF,OAAA;IAAA4D,QAAA,gBAEE5D,OAAA;MAAK6D,SAAS,EAAC,mBAAmB;MAAAD,QAAA,gBAEhC5D,OAAA,CAACJ,IAAI;QACHkE,EAAE,EAAEH,cAAc,CAAC,CAAE;QACrBI,OAAO,EAAE3D,WAAY;QACrByD,SAAS,EAAC,yHAAyH;QAAAD,QAAA,EAElIzD;MAAK;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGPnE,OAAA;QACE+D,OAAO,EAAEL,YAAa;QACtBG,SAAS,EAAC,2EAA2E;QACrF,iBAAevD,MAAO;QACtB,iBAAe,mBAAmBJ,IAAI,EAAG;QACzC,cAAY,UAAUC,KAAK,UAAW;QAAAyD,QAAA,eAEtC5D,OAAA,CAACH,WAAW;UACVuE,IAAI,EAAE,EAAG;UACTP,SAAS,EAAE,qCACTvD,MAAM,GAAG,YAAY,GAAG,EAAE;QACzB;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNnE,OAAA;MACEkB,EAAE,EAAE,mBAAmBhB,IAAI,EAAG;MAC9B2D,SAAS,EAAE;AACnB;AACA,YAAYvD,MAAM,GAAG,sBAAsB,GAAG,mBAAmB;AACjE,SAAU;MAAAsD,QAAA,eAEF5D,OAAA;QAAK6D,SAAS,EAAC,YAAY;QAAAD,QAAA,EACxBlD,OAAO,gBACNV,OAAA;UAAK6D,SAAS,EAAC,uBAAuB;UAAAD,QAAA,gBACpC5D,OAAA;YAAK6D,SAAS,EAAC;UAA0E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChGnE,OAAA;YAAM6D,SAAS,EAAC,uBAAuB;YAAAD,QAAA,EAAC;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,GACJ3D,KAAK,CAACM,MAAM,KAAK,CAAC,gBACpBd,OAAA;UAAK6D,SAAS,EAAC,6CAA6C;UAAAD,QAAA,EAAC;QAE7D;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,gBAENnE,OAAA;UAAK6D,SAAS,EAAC,MAAM;UAAAD,QAAA,EAClBpD,KAAK,CAACmB,GAAG,CAAE0C,IAAI,iBACdrE,OAAA,CAACJ,IAAI;YAEHkE,EAAE,EAAEO,IAAI,CAAChD,IAAK;YACd0C,OAAO,EAAE3D,WAAY;YACrByD,SAAS,EAAC,oIAAoI;YAAAD,QAAA,gBAE9I5D,OAAA;cAAA4D,QAAA,EAAOS,IAAI,CAAClE;YAAK;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACxBE,IAAI,CAAC/C,KAAK,KAAKgD,SAAS,iBACvBtE,OAAA;cAAM6D,SAAS,EAAC,0DAA0D;cAAAD,QAAA,EACvES,IAAI,CAAC/C;YAAK;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CACP;UAAA,GAVIE,IAAI,CAACnD,EAAE;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWR,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9D,EAAA,CA3LIJ,wBAAiE;EAAA,QAQ5CH,WAAW;AAAA;AAAAyE,EAAA,GARhCtE,wBAAiE;AA6LvE,eAAeA,wBAAwB;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}