{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/MasterPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Helmet } from 'react-helmet-async';\nimport { useLanguage } from '../context/LanguageContext';\nimport EnhancedScholarshipCard from '../components/EnhancedScholarshipCard';\nimport ProfessionalSidebar from '../components/ProfessionalSidebar';\nimport { Pagination, Spin, Alert } from 'antd';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MasterPage = () => {\n  _s();\n  const {\n    translations\n  } = useLanguage();\n  const [scholarships, setScholarships] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [pagination, setPagination] = useState({\n    total: 0,\n    page: 1,\n    limit: 12,\n    totalPages: 0,\n    hasNextPage: false,\n    hasPreviousPage: false\n  });\n  const fetchScholarships = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const params = new URLSearchParams({\n        level: 'Master',\n        page: pagination.page.toString(),\n        limit: pagination.limit.toString()\n      });\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n      const response = await fetch(`${apiUrl}/api/scholarships/search?${params.toString()}`);\n      if (!response.ok) {\n        throw new Error('Failed to fetch scholarships');\n      }\n      const data = await response.json();\n      if (data.success) {\n        setScholarships(data.data || []);\n        setPagination(prev => {\n          var _data$pagination, _data$pagination2, _data$pagination3, _data$pagination4;\n          return {\n            ...prev,\n            total: ((_data$pagination = data.pagination) === null || _data$pagination === void 0 ? void 0 : _data$pagination.total) || 0,\n            totalPages: ((_data$pagination2 = data.pagination) === null || _data$pagination2 === void 0 ? void 0 : _data$pagination2.totalPages) || 0,\n            hasNextPage: ((_data$pagination3 = data.pagination) === null || _data$pagination3 === void 0 ? void 0 : _data$pagination3.hasNextPage) || false,\n            hasPreviousPage: ((_data$pagination4 = data.pagination) === null || _data$pagination4 === void 0 ? void 0 : _data$pagination4.hasPreviousPage) || false\n          };\n        });\n      } else {\n        throw new Error(data.message || 'Failed to fetch scholarships');\n      }\n    } catch (error) {\n      console.error('Error fetching master scholarships:', error);\n      setError('Erreur lors du chargement des bourses. Veuillez réessayer.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchScholarships();\n  }, [pagination.page]);\n  const handlePageChange = page => {\n    setPagination(prev => ({\n      ...prev,\n      page\n    }));\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n  const sidebarConfig = {\n    type: 'levels',\n    currentItem: 'Master',\n    limit: 10\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Bourses d'\\xC9tudes de Master | Financement pour \\xC9tudes Sup\\xE9rieures de 2\\xE8me Cycle\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Explorez des opportunit\\xE9s de bourses d'\\xE9tudes pour programmes de Master. Financez vos \\xE9tudes sup\\xE9rieures avec des bourses internationales prestigieuses.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"keywords\",\n        content: \"bourses master, financement master, bourses deuxi\\xE8me cycle, \\xE9tudes sup\\xE9rieures\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-purple-600 to-indigo-700 text-white py-20\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-flex items-center px-4 py-2 bg-purple-500/20 rounded-full text-purple-100 text-sm font-medium mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this), pagination.total, \" bourses disponibles\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl md:text-6xl font-bold mb-6 leading-tight\",\n              children: [\"Bourses d'\\xC9tudes de\", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"block text-yellow-300\",\n                children: \"Master\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xl md:text-2xl text-purple-100 max-w-4xl mx-auto mb-8 leading-relaxed\",\n              children: \"Propulsez votre carri\\xE8re avec des bourses d'\\xE9tudes de Master prestigieuses. Acc\\xE9dez aux meilleures universit\\xE9s mondiales et sp\\xE9cialisez-vous dans votre domaine d'expertise avec un financement complet.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-3xl font-bold text-yellow-300 mb-2\",\n                  children: \"300+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-purple-100\",\n                  children: \"Programmes Master\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-3xl font-bold text-yellow-300 mb-2\",\n                  children: \"40+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-purple-100\",\n                  children: \"Universit\\xE9s Partenaires\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-3xl font-bold text-yellow-300 mb-2\",\n                  children: \"\\u20AC50K+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-purple-100\",\n                  children: \"Financement Moyen\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"lg:w-2/3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl shadow-lg p-8 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                children: \"Pourquoi Poursuivre un Master avec une Bourse ?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"prose prose-purple max-w-none\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 leading-relaxed mb-4\",\n                  children: \"Un Master repr\\xE9sente un investissement strat\\xE9gique dans votre avenir professionnel. Avec une bourse d'\\xE9tudes, vous pouvez vous concentrer pleinement sur l'excellence acad\\xE9mique et la recherche, sans les contraintes financi\\xE8res.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mt-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"w-4 h-4 text-purple-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 2,\n                          d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 166,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 165,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 164,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: \"Sp\\xE9cialisation Avanc\\xE9e\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 170,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-gray-600 text-sm\",\n                        children: \"Expertise approfondie dans votre domaine de choix\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 171,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 169,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 163,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-shrink-0 w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center mt-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"w-4 h-4 text-indigo-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 2,\n                          d: \"M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 177,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 176,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 175,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: \"Opportunit\\xE9s Carri\\xE8re\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 181,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-gray-600 text-sm\",\n                        children: \"Acc\\xE8s \\xE0 des postes de direction et de recherche\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 182,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 180,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"w-4 h-4 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 2,\n                          d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 188,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 187,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: \"R\\xE9seau International\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 192,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-gray-600 text-sm\",\n                        children: \"Connexions mondiales avec experts et professionnels\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 193,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 191,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 185,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-shrink-0 w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mt-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"w-4 h-4 text-yellow-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 2,\n                          d: \"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 199,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 198,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 197,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: \"Innovation & Recherche\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 203,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-gray-600 text-sm\",\n                        children: \"Participation \\xE0 des projets de recherche de pointe\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 204,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 202,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-bold text-gray-900\",\n                  children: \"Bourses de Master Disponibles\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: !loading && !error && `${pagination.total} résultats`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-center items-center py-16\",\n                children: /*#__PURE__*/_jsxDEV(Spin, {\n                  size: \"large\",\n                  tip: \"Chargement des bourses...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 19\n              }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n                message: \"Erreur\",\n                description: error,\n                type: \"error\",\n                showIcon: true,\n                className: \"mb-6 rounded-xl shadow-md\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-2\",\n                  children: scholarships.map((scholarship, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"animate-fade-in\",\n                    style: {\n                      animationDelay: `${index * 0.1}s`\n                    },\n                    children: /*#__PURE__*/_jsxDEV(EnhancedScholarshipCard, {\n                      id: scholarship.id,\n                      title: scholarship.title,\n                      thumbnail: scholarship.thumbnail,\n                      deadline: scholarship.deadline,\n                      isOpen: scholarship.isOpen,\n                      level: scholarship.level,\n                      country: scholarship.country,\n                      fundingSource: scholarship.fundingSource\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 239,\n                      columnNumber: 27\n                    }, this)\n                  }, scholarship.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 21\n                }, this), pagination.total > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-center mt-12\",\n                  children: /*#__PURE__*/_jsxDEV(Pagination, {\n                    current: pagination.page,\n                    total: pagination.total,\n                    pageSize: pagination.limit,\n                    onChange: handlePageChange,\n                    showSizeChanger: false,\n                    showQuickJumper: true,\n                    showTotal: total => `Total ${total} bourses`,\n                    className: \"shadow-sm rounded-xl p-2 bg-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProfessionalSidebar, {\n            config: sidebarConfig,\n            className: \"lg:w-1/3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(MasterPage, \"tY76+N1AuoLNPsF1IJd7vW0yoZc=\", false, function () {\n  return [useLanguage];\n});\n_c = MasterPage;\nexport default MasterPage;\nvar _c;\n$RefreshReg$(_c, \"MasterPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON>", "useLanguage", "EnhancedScholarshipCard", "ProfessionalSidebar", "Pagination", "Spin", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MasterPage", "_s", "translations", "scholarships", "setScholarships", "loading", "setLoading", "error", "setError", "pagination", "setPagination", "total", "page", "limit", "totalPages", "hasNextPage", "hasPreviousPage", "fetchScholarships", "params", "URLSearchParams", "level", "toString", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "fetch", "ok", "Error", "data", "json", "success", "prev", "_data$pagination", "_data$pagination2", "_data$pagination3", "_data$pagination4", "message", "console", "handlePageChange", "window", "scrollTo", "top", "behavior", "sidebarConfig", "type", "currentItem", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "className", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "size", "tip", "description", "showIcon", "map", "scholarship", "index", "style", "animationDelay", "id", "title", "thumbnail", "deadline", "isOpen", "country", "fundingSource", "current", "pageSize", "onChange", "showSizeChanger", "showQuickJumper", "showTotal", "config", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/MasterPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Helmet } from 'react-helmet-async';\nimport { useLanguage } from '../context/LanguageContext';\nimport EnhancedScholarshipCard from '../components/EnhancedScholarshipCard';\nimport ProfessionalSidebar from '../components/ProfessionalSidebar';\nimport PageEndSuggestions from '../components/PageEndSuggestions';\nimport { Pagination, Spin, Alert } from 'antd';\n\ninterface Scholarship {\n  id: number;\n  title: string;\n  description: string;\n  level: string;\n  country: string;\n  deadline: string;\n  isOpen: boolean;\n  thumbnail: string;\n  fundingSource?: string;\n}\n\ninterface PaginationData {\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n  hasNextPage: boolean;\n  hasPreviousPage: boolean;\n}\n\nconst MasterPage: React.FC = () => {\n  const { translations } = useLanguage();\n  const [scholarships, setScholarships] = useState<Scholarship[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [pagination, setPagination] = useState<PaginationData>({\n    total: 0,\n    page: 1,\n    limit: 12,\n    totalPages: 0,\n    hasNextPage: false,\n    hasPreviousPage: false\n  });\n\n  const fetchScholarships = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const params = new URLSearchParams({\n        level: 'Master',\n        page: pagination.page.toString(),\n        limit: pagination.limit.toString()\n      });\n\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n      const response = await fetch(`${apiUrl}/api/scholarships/search?${params.toString()}`);\n      \n      if (!response.ok) {\n        throw new Error('Failed to fetch scholarships');\n      }\n\n      const data = await response.json();\n      \n      if (data.success) {\n        setScholarships(data.data || []);\n        setPagination(prev => ({\n          ...prev,\n          total: data.pagination?.total || 0,\n          totalPages: data.pagination?.totalPages || 0,\n          hasNextPage: data.pagination?.hasNextPage || false,\n          hasPreviousPage: data.pagination?.hasPreviousPage || false\n        }));\n      } else {\n        throw new Error(data.message || 'Failed to fetch scholarships');\n      }\n    } catch (error) {\n      console.error('Error fetching master scholarships:', error);\n      setError('Erreur lors du chargement des bourses. Veuillez réessayer.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchScholarships();\n  }, [pagination.page]);\n\n  const handlePageChange = (page: number) => {\n    setPagination(prev => ({ ...prev, page }));\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  const sidebarConfig = {\n    type: 'levels' as const,\n    currentItem: 'Master',\n    limit: 10\n  };\n\n  return (\n    <>\n      <Helmet>\n        <title>Bourses d'Études de Master | Financement pour Études Supérieures de 2ème Cycle</title>\n        <meta name=\"description\" content=\"Explorez des opportunités de bourses d'études pour programmes de Master. Financez vos études supérieures avec des bourses internationales prestigieuses.\" />\n        <meta name=\"keywords\" content=\"bourses master, financement master, bourses deuxième cycle, études supérieures\" />\n      </Helmet>\n\n      <div className=\"min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100\">\n        {/* Hero Section */}\n        <div className=\"bg-gradient-to-r from-purple-600 to-indigo-700 text-white py-20\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center\">\n              <div className=\"inline-flex items-center px-4 py-2 bg-purple-500/20 rounded-full text-purple-100 text-sm font-medium mb-6\">\n                <span className=\"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"></span>\n                {pagination.total} bourses disponibles\n              </div>\n              \n              <h1 className=\"text-4xl md:text-6xl font-bold mb-6 leading-tight\">\n                Bourses d'Études de\n                <span className=\"block text-yellow-300\">Master</span>\n              </h1>\n              \n              <p className=\"text-xl md:text-2xl text-purple-100 max-w-4xl mx-auto mb-8 leading-relaxed\">\n                Propulsez votre carrière avec des bourses d'études de Master prestigieuses. \n                Accédez aux meilleures universités mondiales et spécialisez-vous dans votre domaine \n                d'expertise avec un financement complet.\n              </p>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-12\">\n                <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\">\n                  <div className=\"text-3xl font-bold text-yellow-300 mb-2\">300+</div>\n                  <div className=\"text-purple-100\">Programmes Master</div>\n                </div>\n                <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\">\n                  <div className=\"text-3xl font-bold text-yellow-300 mb-2\">40+</div>\n                  <div className=\"text-purple-100\">Universités Partenaires</div>\n                </div>\n                <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\">\n                  <div className=\"text-3xl font-bold text-yellow-300 mb-2\">€50K+</div>\n                  <div className=\"text-purple-100\">Financement Moyen</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Content Section */}\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n          <div className=\"flex flex-col lg:flex-row gap-8\">\n            {/* Main Content */}\n            <div className=\"lg:w-2/3\">\n              {/* Info Section */}\n              <div className=\"bg-white rounded-2xl shadow-lg p-8 mb-8\">\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                  Pourquoi Poursuivre un Master avec une Bourse ?\n                </h2>\n                <div className=\"prose prose-purple max-w-none\">\n                  <p className=\"text-gray-600 leading-relaxed mb-4\">\n                    Un Master représente un investissement stratégique dans votre avenir professionnel. \n                    Avec une bourse d'études, vous pouvez vous concentrer pleinement sur l'excellence académique \n                    et la recherche, sans les contraintes financières.\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6\">\n                    <div className=\"flex items-start space-x-3\">\n                      <div className=\"flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mt-1\">\n                        <svg className=\"w-4 h-4 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                        </svg>\n                      </div>\n                      <div>\n                        <h4 className=\"font-semibold text-gray-900\">Spécialisation Avancée</h4>\n                        <p className=\"text-gray-600 text-sm\">Expertise approfondie dans votre domaine de choix</p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-start space-x-3\">\n                      <div className=\"flex-shrink-0 w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center mt-1\">\n                        <svg className=\"w-4 h-4 text-indigo-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                        </svg>\n                      </div>\n                      <div>\n                        <h4 className=\"font-semibold text-gray-900\">Opportunités Carrière</h4>\n                        <p className=\"text-gray-600 text-sm\">Accès à des postes de direction et de recherche</p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-start space-x-3\">\n                      <div className=\"flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1\">\n                        <svg className=\"w-4 h-4 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                        </svg>\n                      </div>\n                      <div>\n                        <h4 className=\"font-semibold text-gray-900\">Réseau International</h4>\n                        <p className=\"text-gray-600 text-sm\">Connexions mondiales avec experts et professionnels</p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-start space-x-3\">\n                      <div className=\"flex-shrink-0 w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mt-1\">\n                        <svg className=\"w-4 h-4 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n                        </svg>\n                      </div>\n                      <div>\n                        <h4 className=\"font-semibold text-gray-900\">Innovation & Recherche</h4>\n                        <p className=\"text-gray-600 text-sm\">Participation à des projets de recherche de pointe</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Scholarships Grid */}\n              <div className=\"mb-8\">\n                <div className=\"flex justify-between items-center mb-6\">\n                  <h2 className=\"text-2xl font-bold text-gray-900\">\n                    Bourses de Master Disponibles\n                  </h2>\n                  <div className=\"text-sm text-gray-600\">\n                    {!loading && !error && `${pagination.total} résultats`}\n                  </div>\n                </div>\n\n                {loading ? (\n                  <div className=\"flex justify-center items-center py-16\">\n                    <Spin size=\"large\" tip=\"Chargement des bourses...\" />\n                  </div>\n                ) : error ? (\n                  <Alert\n                    message=\"Erreur\"\n                    description={error}\n                    type=\"error\"\n                    showIcon\n                    className=\"mb-6 rounded-xl shadow-md\"\n                  />\n                ) : (\n                  <>\n                    <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-2\">\n                      {scholarships.map((scholarship, index) => (\n                        <div key={scholarship.id} className=\"animate-fade-in\" style={{ animationDelay: `${index * 0.1}s` }}>\n                          <EnhancedScholarshipCard\n                            id={scholarship.id}\n                            title={scholarship.title}\n                            thumbnail={scholarship.thumbnail}\n                            deadline={scholarship.deadline}\n                            isOpen={scholarship.isOpen}\n                            level={scholarship.level}\n                            country={scholarship.country}\n                            fundingSource={scholarship.fundingSource}\n                          />\n                        </div>\n                      ))}\n                    </div>\n\n                    {/* Pagination */}\n                    {pagination.total > 0 && (\n                      <div className=\"flex justify-center mt-12\">\n                        <Pagination\n                          current={pagination.page}\n                          total={pagination.total}\n                          pageSize={pagination.limit}\n                          onChange={handlePageChange}\n                          showSizeChanger={false}\n                          showQuickJumper\n                          showTotal={(total) => `Total ${total} bourses`}\n                          className=\"shadow-sm rounded-xl p-2 bg-white\"\n                        />\n                      </div>\n                    )}\n                  </>\n                )}\n              </div>\n            </div>\n\n            {/* Sidebar */}\n            <ProfessionalSidebar \n              config={sidebarConfig}\n              className=\"lg:w-1/3\"\n            />\n          </div>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default MasterPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,WAAW,QAAQ,4BAA4B;AACxD,OAAOC,uBAAuB,MAAM,uCAAuC;AAC3E,OAAOC,mBAAmB,MAAM,mCAAmC;AAEnE,SAASC,UAAU,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAuB/C,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM;IAAEC;EAAa,CAAC,GAAGZ,WAAW,CAAC,CAAC;EACtC,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAiB;IAC3DwB,KAAK,EAAE,CAAC;IACRC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE;EACnB,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMU,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCC,KAAK,EAAE,QAAQ;QACfR,IAAI,EAAEH,UAAU,CAACG,IAAI,CAACS,QAAQ,CAAC,CAAC;QAChCR,KAAK,EAAEJ,UAAU,CAACI,KAAK,CAACQ,QAAQ,CAAC;MACnC,CAAC,CAAC;MAEF,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,MAAM,4BAA4BJ,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;MAEtF,IAAI,CAACK,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;MACjD;MAEA,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChB5B,eAAe,CAAC0B,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;QAChCpB,aAAa,CAACuB,IAAI;UAAA,IAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;UAAA,OAAK;YACrB,GAAGJ,IAAI;YACPtB,KAAK,EAAE,EAAAuB,gBAAA,GAAAJ,IAAI,CAACrB,UAAU,cAAAyB,gBAAA,uBAAfA,gBAAA,CAAiBvB,KAAK,KAAI,CAAC;YAClCG,UAAU,EAAE,EAAAqB,iBAAA,GAAAL,IAAI,CAACrB,UAAU,cAAA0B,iBAAA,uBAAfA,iBAAA,CAAiBrB,UAAU,KAAI,CAAC;YAC5CC,WAAW,EAAE,EAAAqB,iBAAA,GAAAN,IAAI,CAACrB,UAAU,cAAA2B,iBAAA,uBAAfA,iBAAA,CAAiBrB,WAAW,KAAI,KAAK;YAClDC,eAAe,EAAE,EAAAqB,iBAAA,GAAAP,IAAI,CAACrB,UAAU,cAAA4B,iBAAA,uBAAfA,iBAAA,CAAiBrB,eAAe,KAAI;UACvD,CAAC;QAAA,CAAC,CAAC;MACL,CAAC,MAAM;QACL,MAAM,IAAIa,KAAK,CAACC,IAAI,CAACQ,OAAO,IAAI,8BAA8B,CAAC;MACjE;IACF,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdgC,OAAO,CAAChC,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3DC,QAAQ,CAAC,4DAA4D,CAAC;IACxE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDlB,SAAS,CAAC,MAAM;IACd6B,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACR,UAAU,CAACG,IAAI,CAAC,CAAC;EAErB,MAAM4B,gBAAgB,GAAI5B,IAAY,IAAK;IACzCF,aAAa,CAACuB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAErB;IAAK,CAAC,CAAC,CAAC;IAC1C6B,MAAM,CAACC,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACjD,CAAC;EAED,MAAMC,aAAa,GAAG;IACpBC,IAAI,EAAE,QAAiB;IACvBC,WAAW,EAAE,QAAQ;IACrBlC,KAAK,EAAE;EACT,CAAC;EAED,oBACEhB,OAAA,CAAAE,SAAA;IAAAiD,QAAA,gBACEnD,OAAA,CAACR,MAAM;MAAA2D,QAAA,gBACLnD,OAAA;QAAAmD,QAAA,EAAO;MAA8E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC7FvD,OAAA;QAAMwD,IAAI,EAAC,aAAa;QAACC,OAAO,EAAC;MAA0J;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9LvD,OAAA;QAAMwD,IAAI,EAAC,UAAU;QAACC,OAAO,EAAC;MAAgF;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3G,CAAC,eAETvD,OAAA;MAAK0D,SAAS,EAAC,6DAA6D;MAAAP,QAAA,gBAE1EnD,OAAA;QAAK0D,SAAS,EAAC,iEAAiE;QAAAP,QAAA,eAC9EnD,OAAA;UAAK0D,SAAS,EAAC,wCAAwC;UAAAP,QAAA,eACrDnD,OAAA;YAAK0D,SAAS,EAAC,aAAa;YAAAP,QAAA,gBAC1BnD,OAAA;cAAK0D,SAAS,EAAC,2GAA2G;cAAAP,QAAA,gBACxHnD,OAAA;gBAAM0D,SAAS,EAAC;cAAsD;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC7E3C,UAAU,CAACE,KAAK,EAAC,sBACpB;YAAA;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAENvD,OAAA;cAAI0D,SAAS,EAAC,mDAAmD;cAAAP,QAAA,GAAC,wBAEhE,eAAAnD,OAAA;gBAAM0D,SAAS,EAAC,uBAAuB;gBAAAP,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eAELvD,OAAA;cAAG0D,SAAS,EAAC,4EAA4E;cAAAP,QAAA,EAAC;YAI1F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJvD,OAAA;cAAK0D,SAAS,EAAC,+DAA+D;cAAAP,QAAA,gBAC5EnD,OAAA;gBAAK0D,SAAS,EAAC,oEAAoE;gBAAAP,QAAA,gBACjFnD,OAAA;kBAAK0D,SAAS,EAAC,yCAAyC;kBAAAP,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnEvD,OAAA;kBAAK0D,SAAS,EAAC,iBAAiB;kBAAAP,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACNvD,OAAA;gBAAK0D,SAAS,EAAC,oEAAoE;gBAAAP,QAAA,gBACjFnD,OAAA;kBAAK0D,SAAS,EAAC,yCAAyC;kBAAAP,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClEvD,OAAA;kBAAK0D,SAAS,EAAC,iBAAiB;kBAAAP,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACNvD,OAAA;gBAAK0D,SAAS,EAAC,oEAAoE;gBAAAP,QAAA,gBACjFnD,OAAA;kBAAK0D,SAAS,EAAC,yCAAyC;kBAAAP,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpEvD,OAAA;kBAAK0D,SAAS,EAAC,iBAAiB;kBAAAP,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvD,OAAA;QAAK0D,SAAS,EAAC,8CAA8C;QAAAP,QAAA,eAC3DnD,OAAA;UAAK0D,SAAS,EAAC,iCAAiC;UAAAP,QAAA,gBAE9CnD,OAAA;YAAK0D,SAAS,EAAC,UAAU;YAAAP,QAAA,gBAEvBnD,OAAA;cAAK0D,SAAS,EAAC,yCAAyC;cAAAP,QAAA,gBACtDnD,OAAA;gBAAI0D,SAAS,EAAC,uCAAuC;gBAAAP,QAAA,EAAC;cAEtD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLvD,OAAA;gBAAK0D,SAAS,EAAC,+BAA+B;gBAAAP,QAAA,gBAC5CnD,OAAA;kBAAG0D,SAAS,EAAC,oCAAoC;kBAAAP,QAAA,EAAC;gBAIlD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJvD,OAAA;kBAAK0D,SAAS,EAAC,4CAA4C;kBAAAP,QAAA,gBACzDnD,OAAA;oBAAK0D,SAAS,EAAC,4BAA4B;oBAAAP,QAAA,gBACzCnD,OAAA;sBAAK0D,SAAS,EAAC,wFAAwF;sBAAAP,QAAA,eACrGnD,OAAA;wBAAK0D,SAAS,EAAC,yBAAyB;wBAACC,IAAI,EAAC,MAAM;wBAACC,MAAM,EAAC,cAAc;wBAACC,OAAO,EAAC,WAAW;wBAAAV,QAAA,eAC5FnD,OAAA;0BAAM8D,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,WAAW,EAAE,CAAE;0BAACC,CAAC,EAAC;wBAA4B;0BAAAb,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNvD,OAAA;sBAAAmD,QAAA,gBACEnD,OAAA;wBAAI0D,SAAS,EAAC,6BAA6B;wBAAAP,QAAA,EAAC;sBAAsB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACvEvD,OAAA;wBAAG0D,SAAS,EAAC,uBAAuB;wBAAAP,QAAA,EAAC;sBAAiD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvD,OAAA;oBAAK0D,SAAS,EAAC,4BAA4B;oBAAAP,QAAA,gBACzCnD,OAAA;sBAAK0D,SAAS,EAAC,wFAAwF;sBAAAP,QAAA,eACrGnD,OAAA;wBAAK0D,SAAS,EAAC,yBAAyB;wBAACC,IAAI,EAAC,MAAM;wBAACC,MAAM,EAAC,cAAc;wBAACC,OAAO,EAAC,WAAW;wBAAAV,QAAA,eAC5FnD,OAAA;0BAAM8D,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,WAAW,EAAE,CAAE;0BAACC,CAAC,EAAC;wBAA8K;0BAAAb,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnP;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNvD,OAAA;sBAAAmD,QAAA,gBACEnD,OAAA;wBAAI0D,SAAS,EAAC,6BAA6B;wBAAAP,QAAA,EAAC;sBAAqB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACtEvD,OAAA;wBAAG0D,SAAS,EAAC,uBAAuB;wBAAAP,QAAA,EAAC;sBAA+C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvD,OAAA;oBAAK0D,SAAS,EAAC,4BAA4B;oBAAAP,QAAA,gBACzCnD,OAAA;sBAAK0D,SAAS,EAAC,uFAAuF;sBAAAP,QAAA,eACpGnD,OAAA;wBAAK0D,SAAS,EAAC,wBAAwB;wBAACC,IAAI,EAAC,MAAM;wBAACC,MAAM,EAAC,cAAc;wBAACC,OAAO,EAAC,WAAW;wBAAAV,QAAA,eAC3FnD,OAAA;0BAAM8D,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,WAAW,EAAE,CAAE;0BAACC,CAAC,EAAC;wBAAwQ;0BAAAb,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7U;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNvD,OAAA;sBAAAmD,QAAA,gBACEnD,OAAA;wBAAI0D,SAAS,EAAC,6BAA6B;wBAAAP,QAAA,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACrEvD,OAAA;wBAAG0D,SAAS,EAAC,uBAAuB;wBAAAP,QAAA,EAAC;sBAAmD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvD,OAAA;oBAAK0D,SAAS,EAAC,4BAA4B;oBAAAP,QAAA,gBACzCnD,OAAA;sBAAK0D,SAAS,EAAC,wFAAwF;sBAAAP,QAAA,eACrGnD,OAAA;wBAAK0D,SAAS,EAAC,yBAAyB;wBAACC,IAAI,EAAC,MAAM;wBAACC,MAAM,EAAC,cAAc;wBAACC,OAAO,EAAC,WAAW;wBAAAV,QAAA,eAC5FnD,OAAA;0BAAM8D,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,WAAW,EAAE,CAAE;0BAACC,CAAC,EAAC;wBAAkN;0BAAAb,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvR;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNvD,OAAA;sBAAAmD,QAAA,gBACEnD,OAAA;wBAAI0D,SAAS,EAAC,6BAA6B;wBAAAP,QAAA,EAAC;sBAAsB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACvEvD,OAAA;wBAAG0D,SAAS,EAAC,uBAAuB;wBAAAP,QAAA,EAAC;sBAAkD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNvD,OAAA;cAAK0D,SAAS,EAAC,MAAM;cAAAP,QAAA,gBACnBnD,OAAA;gBAAK0D,SAAS,EAAC,wCAAwC;gBAAAP,QAAA,gBACrDnD,OAAA;kBAAI0D,SAAS,EAAC,kCAAkC;kBAAAP,QAAA,EAAC;gBAEjD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLvD,OAAA;kBAAK0D,SAAS,EAAC,uBAAuB;kBAAAP,QAAA,EACnC,CAAC3C,OAAO,IAAI,CAACE,KAAK,IAAI,GAAGE,UAAU,CAACE,KAAK;gBAAY;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAEL/C,OAAO,gBACNR,OAAA;gBAAK0D,SAAS,EAAC,wCAAwC;gBAAAP,QAAA,eACrDnD,OAAA,CAACH,IAAI;kBAACqE,IAAI,EAAC,OAAO;kBAACC,GAAG,EAAC;gBAA2B;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,GACJ7C,KAAK,gBACPV,OAAA,CAACF,KAAK;gBACJ2C,OAAO,EAAC,QAAQ;gBAChB2B,WAAW,EAAE1D,KAAM;gBACnBuC,IAAI,EAAC,OAAO;gBACZoB,QAAQ;gBACRX,SAAS,EAAC;cAA2B;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,gBAEFvD,OAAA,CAAAE,SAAA;gBAAAiD,QAAA,gBACEnD,OAAA;kBAAK0D,SAAS,EAAC,sDAAsD;kBAAAP,QAAA,EAClE7C,YAAY,CAACgE,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBACnCxE,OAAA;oBAA0B0D,SAAS,EAAC,iBAAiB;oBAACe,KAAK,EAAE;sBAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;oBAAI,CAAE;oBAAArB,QAAA,eACjGnD,OAAA,CAACN,uBAAuB;sBACtBiF,EAAE,EAAEJ,WAAW,CAACI,EAAG;sBACnBC,KAAK,EAAEL,WAAW,CAACK,KAAM;sBACzBC,SAAS,EAAEN,WAAW,CAACM,SAAU;sBACjCC,QAAQ,EAAEP,WAAW,CAACO,QAAS;sBAC/BC,MAAM,EAAER,WAAW,CAACQ,MAAO;sBAC3BxD,KAAK,EAAEgD,WAAW,CAAChD,KAAM;sBACzByD,OAAO,EAAET,WAAW,CAACS,OAAQ;sBAC7BC,aAAa,EAAEV,WAAW,CAACU;oBAAc;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C;kBAAC,GAVMgB,WAAW,CAACI,EAAE;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAWnB,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EAGL3C,UAAU,CAACE,KAAK,GAAG,CAAC,iBACnBd,OAAA;kBAAK0D,SAAS,EAAC,2BAA2B;kBAAAP,QAAA,eACxCnD,OAAA,CAACJ,UAAU;oBACTsF,OAAO,EAAEtE,UAAU,CAACG,IAAK;oBACzBD,KAAK,EAAEF,UAAU,CAACE,KAAM;oBACxBqE,QAAQ,EAAEvE,UAAU,CAACI,KAAM;oBAC3BoE,QAAQ,EAAEzC,gBAAiB;oBAC3B0C,eAAe,EAAE,KAAM;oBACvBC,eAAe;oBACfC,SAAS,EAAGzE,KAAK,IAAK,SAASA,KAAK,UAAW;oBAC/C4C,SAAS,EAAC;kBAAmC;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA,eACD,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNvD,OAAA,CAACL,mBAAmB;YAClB6F,MAAM,EAAExC,aAAc;YACtBU,SAAS,EAAC;UAAU;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACnD,EAAA,CA7PID,UAAoB;EAAA,QACCV,WAAW;AAAA;AAAAgG,EAAA,GADhCtF,UAAoB;AA+P1B,eAAeA,UAAU;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}