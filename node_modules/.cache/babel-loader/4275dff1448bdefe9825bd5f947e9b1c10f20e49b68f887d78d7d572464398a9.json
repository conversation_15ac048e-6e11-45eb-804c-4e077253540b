{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Countries.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useLanguage } from '../context/LanguageContext';\nimport ProfessionalSidebar from '../components/ProfessionalSidebar';\nimport PageEndSuggestions from '../components/PageEndSuggestions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Countries = () => {\n  _s();\n  const {\n    translations\n  } = useLanguage();\n  const [countries, setCountries] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredCountries, setFilteredCountries] = useState([]);\n  useEffect(() => {\n    fetchCountries();\n  }, []);\n  useEffect(() => {\n    if (searchTerm.trim() === '') {\n      setFilteredCountries(countries);\n    } else {\n      const filtered = countries.filter(country => country.country.toLowerCase().includes(searchTerm.toLowerCase()));\n      setFilteredCountries(filtered);\n    }\n  }, [searchTerm, countries]);\n  const fetchCountries = async () => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n      const response = await fetch(`${apiUrl}/api/countries`);\n      if (response.ok) {\n        const data = await response.json();\n        setCountries(data.data || []);\n      } else {\n        console.error('Failed to fetch countries');\n      }\n    } catch (error) {\n      console.error('Error fetching countries:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getCountryFlag = countryName => {\n    const flagMap = {\n      'France': '🇫🇷',\n      'Germany': '🇩🇪',\n      'United Kingdom': '🇬🇧',\n      'United States': '🇺🇸',\n      'Canada': '🇨🇦',\n      'Australia': '🇦🇺',\n      'Netherlands': '🇳🇱',\n      'Sweden': '🇸🇪',\n      'Norway': '🇳🇴',\n      'Denmark': '🇩🇰',\n      'Switzerland': '🇨🇭',\n      'Belgium': '🇧🇪',\n      'Austria': '🇦🇹',\n      'Italy': '🇮🇹',\n      'Spain': '🇪🇸',\n      'Japan': '🇯🇵',\n      'South Korea': '🇰🇷',\n      'Singapore': '🇸🇬',\n      'New Zealand': '🇳🇿',\n      'Finland': '🇫🇮'\n    };\n    return flagMap[countryName] || '🌍';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 pt-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-4 text-gray-600\",\n            children: \"Chargement des pays...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-flex items-center px-4 py-2 bg-blue-500/20 rounded-full text-blue-100 text-sm font-medium mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), countries.length, \" pays disponibles\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl md:text-6xl font-bold mb-6 leading-tight\",\n            children: [\"Bourses d'\\xC9tudes par\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block text-yellow-300\",\n              children: \"Pays\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl md:text-2xl text-blue-100 max-w-4xl mx-auto mb-8 leading-relaxed\",\n            children: \"Explorez des opportunit\\xE9s de bourses d'\\xE9tudes dans le monde entier. Chaque pays offre des programmes uniques adapt\\xE9s aux \\xE9tudiants internationaux, avec des avantages culturels et acad\\xE9miques exceptionnels.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-yellow-300 mb-2\",\n                children: [countries.length, \"+\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-blue-100\",\n                children: \"Pays Partenaires\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-yellow-300 mb-2\",\n                children: [countries.reduce((total, country) => total + country.count, 0), \"+\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-blue-100\",\n                children: \"Bourses Disponibles\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-yellow-300 mb-2\",\n                children: \"100%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-blue-100\",\n                children: \"Gratuit & Accessible\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-md mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: translations.countries.searchPlaceholder,\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-5 w-5 text-gray-400\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col lg:flex-row gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:w-2/3\",\n          children: filteredCountries.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-6xl mb-4\",\n              children: \"\\uD83D\\uDD0D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-2\",\n              children: \"Aucun pays trouv\\xE9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Essayez de modifier votre recherche ou parcourez tous les pays disponibles.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 11\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n            children: filteredCountries.map(country => /*#__PURE__*/_jsxDEV(Link, {\n              to: `/countries/${encodeURIComponent(country.country)}`,\n              className: \"group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-blue-200\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-4xl\",\n                    children: getCountryFlag(country.country)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-2xl font-bold text-blue-600\",\n                      children: country.count\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-500\",\n                      children: country.count === 1 ? 'bourse' : 'bourses'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 184,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 mb-2\",\n                  children: country.country\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center text-blue-600 text-sm font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: translations.countries.viewScholarships\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M9 5l7 7-7 7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 197,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)\n            }, country.country, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ProfessionalSidebar, {\n          config: {\n            type: 'countries',\n            limit: 10\n          },\n          className: \"lg:w-1/3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white py-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-bold text-gray-900 mb-4\",\n            children: \"Statistiques Globales\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600\",\n            children: \"D\\xE9couvrez la r\\xE9partition des bourses par r\\xE9gion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl font-bold text-blue-600 mb-2\",\n              children: countries.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg text-gray-600\",\n              children: \"Pays disponibles\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl font-bold text-green-600 mb-2\",\n              children: countries.reduce((total, country) => total + country.count, 0)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg text-gray-600\",\n              children: \"Total des bourses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl font-bold text-purple-600 mb-2\",\n              children: Math.round(countries.reduce((total, country) => total + country.count, 0) / countries.length) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg text-gray-600\",\n              children: \"Moyenne par pays\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PageEndSuggestions, {\n      currentPageType: \"country\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n};\n_s(Countries, \"FVd9G+D4BO3Hc9pyEiBVjSG+Opo=\", false, function () {\n  return [useLanguage];\n});\n_c = Countries;\nexport default Countries;\nvar _c;\n$RefreshReg$(_c, \"Countries\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLanguage", "ProfessionalSidebar", "PageEndSuggestions", "jsxDEV", "_jsxDEV", "Countries", "_s", "translations", "countries", "setCountries", "loading", "setLoading", "searchTerm", "setSearchTerm", "filteredCountries", "setFilteredCountries", "fetchCountries", "trim", "filtered", "filter", "country", "toLowerCase", "includes", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "fetch", "ok", "data", "json", "console", "error", "getCountryFlag", "countryName", "flagMap", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "reduce", "total", "count", "type", "placeholder", "searchPlaceholder", "value", "onChange", "e", "target", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "map", "to", "encodeURIComponent", "viewScholarships", "config", "limit", "Math", "round", "currentPageType", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Countries.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useLanguage } from '../context/LanguageContext';\nimport ProfessionalSidebar from '../components/ProfessionalSidebar';\nimport PageEndSuggestions from '../components/PageEndSuggestions';\n\ninterface Country {\n  country: string;\n  count: number;\n}\n\nconst Countries: React.FC = () => {\n  const { translations } = useLanguage();\n  const [countries, setCountries] = useState<Country[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredCountries, setFilteredCountries] = useState<Country[]>([]);\n\n  useEffect(() => {\n    fetchCountries();\n  }, []);\n\n  useEffect(() => {\n    if (searchTerm.trim() === '') {\n      setFilteredCountries(countries);\n    } else {\n      const filtered = countries.filter(country =>\n        country.country.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n      setFilteredCountries(filtered);\n    }\n  }, [searchTerm, countries]);\n\n  const fetchCountries = async () => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n      const response = await fetch(`${apiUrl}/api/countries`);\n      if (response.ok) {\n        const data = await response.json();\n        setCountries(data.data || []);\n      } else {\n        console.error('Failed to fetch countries');\n      }\n    } catch (error) {\n      console.error('Error fetching countries:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getCountryFlag = (countryName: string): string => {\n    const flagMap: { [key: string]: string } = {\n      'France': '🇫🇷',\n      'Germany': '🇩🇪',\n      'United Kingdom': '🇬🇧',\n      'United States': '🇺🇸',\n      'Canada': '🇨🇦',\n      'Australia': '🇦🇺',\n      'Netherlands': '🇳🇱',\n      'Sweden': '🇸🇪',\n      'Norway': '🇳🇴',\n      'Denmark': '🇩🇰',\n      'Switzerland': '🇨🇭',\n      'Belgium': '🇧🇪',\n      'Austria': '🇦🇹',\n      'Italy': '🇮🇹',\n      'Spain': '🇪🇸',\n      'Japan': '🇯🇵',\n      'South Korea': '🇰🇷',\n      'Singapore': '🇸🇬',\n      'New Zealand': '🇳🇿',\n      'Finland': '🇫🇮',\n    };\n    return flagMap[countryName] || '🌍';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 pt-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600\">Chargement des pays...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Hero Section */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <div className=\"inline-flex items-center px-4 py-2 bg-blue-500/20 rounded-full text-blue-100 text-sm font-medium mb-6\">\n              <span className=\"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"></span>\n              {countries.length} pays disponibles\n            </div>\n\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6 leading-tight\">\n              Bourses d'Études par\n              <span className=\"block text-yellow-300\">Pays</span>\n            </h1>\n\n            <p className=\"text-xl md:text-2xl text-blue-100 max-w-4xl mx-auto mb-8 leading-relaxed\">\n              Explorez des opportunités de bourses d'études dans le monde entier.\n              Chaque pays offre des programmes uniques adaptés aux étudiants internationaux,\n              avec des avantages culturels et académiques exceptionnels.\n            </p>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-12\">\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\">\n                <div className=\"text-3xl font-bold text-yellow-300 mb-2\">{countries.length}+</div>\n                <div className=\"text-blue-100\">Pays Partenaires</div>\n              </div>\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\">\n                <div className=\"text-3xl font-bold text-yellow-300 mb-2\">\n                  {countries.reduce((total, country) => total + country.count, 0)}+\n                </div>\n                <div className=\"text-blue-100\">Bourses Disponibles</div>\n              </div>\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\">\n                <div className=\"text-3xl font-bold text-yellow-300 mb-2\">100%</div>\n                <div className=\"text-blue-100\">Gratuit & Accessible</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Search Section */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"max-w-md mx-auto\">\n          <div className=\"relative\">\n            <input\n              type=\"text\"\n              placeholder={translations.countries.searchPlaceholder}\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n              </svg>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Content Section */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16\">\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:w-2/3\">\n        {filteredCountries.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">🔍</div>\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n              Aucun pays trouvé\n            </h3>\n            <p className=\"text-gray-600\">\n              Essayez de modifier votre recherche ou parcourez tous les pays disponibles.\n            </p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {filteredCountries.map((country) => (\n              <Link\n                key={country.country}\n                to={`/countries/${encodeURIComponent(country.country)}`}\n                className=\"group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-blue-200\"\n              >\n                <div className=\"p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"text-4xl\">\n                      {getCountryFlag(country.country)}\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-2xl font-bold text-blue-600\">\n                        {country.count}\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        {country.count === 1 ? 'bourse' : 'bourses'}\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <h3 className=\"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 mb-2\">\n                    {country.country}\n                  </h3>\n                  \n                  <div className=\"flex items-center text-blue-600 text-sm font-medium\">\n                    <span>{translations.countries.viewScholarships}</span>\n                    <svg className=\"ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  </div>\n                </div>\n              </Link>\n            ))}\n          </div>\n        )}\n            </div>\n\n            {/* Sidebar */}\n            <ProfessionalSidebar\n              config={{\n                type: 'countries' as const,\n                limit: 10\n              }}\n              className=\"lg:w-1/3\"\n            />\n          </div>\n        </div>\n\n      {/* Statistics Section */}\n      <div className=\"bg-white py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Statistiques Globales\n            </h2>\n            <p className=\"text-lg text-gray-600\">\n              Découvrez la répartition des bourses par région\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl font-bold text-blue-600 mb-2\">\n                {countries.length}\n              </div>\n              <div className=\"text-lg text-gray-600\">\n                Pays disponibles\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-4xl font-bold text-green-600 mb-2\">\n                {countries.reduce((total, country) => total + country.count, 0)}\n              </div>\n              <div className=\"text-lg text-gray-600\">\n                Total des bourses\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-4xl font-bold text-purple-600 mb-2\">\n                {Math.round(countries.reduce((total, country) => total + country.count, 0) / countries.length) || 0}\n              </div>\n              <div className=\"text-lg text-gray-600\">\n                Moyenne par pays\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Page End Suggestions */}\n      <PageEndSuggestions\n        currentPageType=\"country\"\n      />\n    </div>\n  );\n};\n\nexport default Countries;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,WAAW,QAAQ,4BAA4B;AACxD,OAAOC,mBAAmB,MAAM,mCAAmC;AACnE,OAAOC,kBAAkB,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOlE,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAa,CAAC,GAAGP,WAAW,CAAC,CAAC;EACtC,MAAM,CAACQ,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAY,EAAE,CAAC;EACzD,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlB,QAAQ,CAAY,EAAE,CAAC;EAEzEC,SAAS,CAAC,MAAM;IACdkB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAENlB,SAAS,CAAC,MAAM;IACd,IAAIc,UAAU,CAACK,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC5BF,oBAAoB,CAACP,SAAS,CAAC;IACjC,CAAC,MAAM;MACL,MAAMU,QAAQ,GAAGV,SAAS,CAACW,MAAM,CAACC,OAAO,IACvCA,OAAO,CAACA,OAAO,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACV,UAAU,CAACS,WAAW,CAAC,CAAC,CACjE,CAAC;MACDN,oBAAoB,CAACG,QAAQ,CAAC;IAChC;EACF,CAAC,EAAE,CAACN,UAAU,EAAEJ,SAAS,CAAC,CAAC;EAE3B,MAAMQ,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMO,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,MAAM,gBAAgB,CAAC;MACvD,IAAII,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClCtB,YAAY,CAACqB,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MAC/B,CAAC,MAAM;QACLE,OAAO,CAACC,KAAK,CAAC,2BAA2B,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuB,cAAc,GAAIC,WAAmB,IAAa;IACtD,MAAMC,OAAkC,GAAG;MACzC,QAAQ,EAAE,MAAM;MAChB,SAAS,EAAE,MAAM;MACjB,gBAAgB,EAAE,MAAM;MACxB,eAAe,EAAE,MAAM;MACvB,QAAQ,EAAE,MAAM;MAChB,WAAW,EAAE,MAAM;MACnB,aAAa,EAAE,MAAM;MACrB,QAAQ,EAAE,MAAM;MAChB,QAAQ,EAAE,MAAM;MAChB,SAAS,EAAE,MAAM;MACjB,aAAa,EAAE,MAAM;MACrB,SAAS,EAAE,MAAM;MACjB,SAAS,EAAE,MAAM;MACjB,OAAO,EAAE,MAAM;MACf,OAAO,EAAE,MAAM;MACf,OAAO,EAAE,MAAM;MACf,aAAa,EAAE,MAAM;MACrB,WAAW,EAAE,MAAM;MACnB,aAAa,EAAE,MAAM;MACrB,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,OAAO,CAACD,WAAW,CAAC,IAAI,IAAI;EACrC,CAAC;EAED,IAAIzB,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKiC,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9ElC,OAAA;QAAKiC,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC3DlC,OAAA;UAAKiC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BlC,OAAA;YAAKiC,SAAS,EAAC;UAAwE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9FtC,OAAA;YAAGiC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEtC,OAAA;IAAKiC,SAAS,EAAC,2DAA2D;IAAAC,QAAA,gBAExElC,OAAA;MAAKiC,SAAS,EAAC,+DAA+D;MAAAC,QAAA,eAC5ElC,OAAA;QAAKiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDlC,OAAA;UAAKiC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BlC,OAAA;YAAKiC,SAAS,EAAC,uGAAuG;YAAAC,QAAA,gBACpHlC,OAAA;cAAMiC,SAAS,EAAC;YAAsD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC7ElC,SAAS,CAACmC,MAAM,EAAC,mBACpB;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAENtC,OAAA;YAAIiC,SAAS,EAAC,mDAAmD;YAAAC,QAAA,GAAC,yBAEhE,eAAAlC,OAAA;cAAMiC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eAELtC,OAAA;YAAGiC,SAAS,EAAC,0EAA0E;YAAAC,QAAA,EAAC;UAIxF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJtC,OAAA;YAAKiC,SAAS,EAAC,+DAA+D;YAAAC,QAAA,gBAC5ElC,OAAA;cAAKiC,SAAS,EAAC,oEAAoE;cAAAC,QAAA,gBACjFlC,OAAA;gBAAKiC,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,GAAE9B,SAAS,CAACmC,MAAM,EAAC,GAAC;cAAA;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClFtC,OAAA;gBAAKiC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACNtC,OAAA;cAAKiC,SAAS,EAAC,oEAAoE;cAAAC,QAAA,gBACjFlC,OAAA;gBAAKiC,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,GACrD9B,SAAS,CAACoC,MAAM,CAAC,CAACC,KAAK,EAAEzB,OAAO,KAAKyB,KAAK,GAAGzB,OAAO,CAAC0B,KAAK,EAAE,CAAC,CAAC,EAAC,GAClE;cAAA;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtC,OAAA;gBAAKiC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACNtC,OAAA;cAAKiC,SAAS,EAAC,oEAAoE;cAAAC,QAAA,gBACjFlC,OAAA;gBAAKiC,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnEtC,OAAA;gBAAKiC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA;MAAKiC,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DlC,OAAA;QAAKiC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BlC,OAAA;UAAKiC,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBlC,OAAA;YACE2C,IAAI,EAAC,MAAM;YACXC,WAAW,EAAEzC,YAAY,CAACC,SAAS,CAACyC,iBAAkB;YACtDC,KAAK,EAAEtC,UAAW;YAClBuC,QAAQ,EAAGC,CAAC,IAAKvC,aAAa,CAACuC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/Cb,SAAS,EAAC;UAAoH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/H,CAAC,eACFtC,OAAA;YAAKiC,SAAS,EAAC,sEAAsE;YAAAC,QAAA,eACnFlC,OAAA;cAAKiC,SAAS,EAAC,uBAAuB;cAACiB,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAlB,QAAA,eAC1FlC,OAAA;gBAAMqD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA6C;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA;MAAKiC,SAAS,EAAC,8CAA8C;MAAAC,QAAA,eAC3DlC,OAAA;QAAKiC,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAE9ClC,OAAA;UAAKiC,SAAS,EAAC,UAAU;UAAAC,QAAA,EAC1BxB,iBAAiB,CAAC6B,MAAM,KAAK,CAAC,gBAC7BvC,OAAA;YAAKiC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChClC,OAAA;cAAKiC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCtC,OAAA;cAAIiC,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtC,OAAA;cAAGiC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAE7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,gBAENtC,OAAA;YAAKiC,SAAS,EAAC,qEAAqE;YAAAC,QAAA,EACjFxB,iBAAiB,CAAC+C,GAAG,CAAEzC,OAAO,iBAC7BhB,OAAA,CAACL,IAAI;cAEH+D,EAAE,EAAE,cAAcC,kBAAkB,CAAC3C,OAAO,CAACA,OAAO,CAAC,EAAG;cACxDiB,SAAS,EAAC,8IAA8I;cAAAC,QAAA,eAExJlC,OAAA;gBAAKiC,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBlC,OAAA;kBAAKiC,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrDlC,OAAA;oBAAKiC,SAAS,EAAC,UAAU;oBAAAC,QAAA,EACtBJ,cAAc,CAACd,OAAO,CAACA,OAAO;kBAAC;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,eACNtC,OAAA;oBAAKiC,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzBlC,OAAA;sBAAKiC,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC9ClB,OAAO,CAAC0B;oBAAK;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACNtC,OAAA;sBAAKiC,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACnClB,OAAO,CAAC0B,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAG;oBAAS;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENtC,OAAA;kBAAIiC,SAAS,EAAC,mGAAmG;kBAAAC,QAAA,EAC9GlB,OAAO,CAACA;gBAAO;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eAELtC,OAAA;kBAAKiC,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,gBAClElC,OAAA;oBAAAkC,QAAA,EAAO/B,YAAY,CAACC,SAAS,CAACwD;kBAAgB;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtDtC,OAAA;oBAAKiC,SAAS,EAAC,0EAA0E;oBAACiB,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAlB,QAAA,eAC7IlC,OAAA;sBAAMqD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAc;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GA7BDtB,OAAO,CAACA,OAAO;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8BhB,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eAGNtC,OAAA,CAACH,mBAAmB;UAClBgE,MAAM,EAAE;YACNlB,IAAI,EAAE,WAAoB;YAC1BmB,KAAK,EAAE;UACT,CAAE;UACF7B,SAAS,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGRtC,OAAA;MAAKiC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BlC,OAAA;QAAKiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDlC,OAAA;UAAKiC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClC,OAAA;YAAIiC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtC,OAAA;YAAGiC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENtC,OAAA;UAAKiC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDlC,OAAA;YAAKiC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlC,OAAA;cAAKiC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACnD9B,SAAS,CAACmC;YAAM;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACNtC,OAAA;cAAKiC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAEvC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtC,OAAA;YAAKiC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlC,OAAA;cAAKiC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EACpD9B,SAAS,CAACoC,MAAM,CAAC,CAACC,KAAK,EAAEzB,OAAO,KAAKyB,KAAK,GAAGzB,OAAO,CAAC0B,KAAK,EAAE,CAAC;YAAC;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACNtC,OAAA;cAAKiC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAEvC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtC,OAAA;YAAKiC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlC,OAAA;cAAKiC,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EACrD6B,IAAI,CAACC,KAAK,CAAC5D,SAAS,CAACoC,MAAM,CAAC,CAACC,KAAK,EAAEzB,OAAO,KAAKyB,KAAK,GAAGzB,OAAO,CAAC0B,KAAK,EAAE,CAAC,CAAC,GAAGtC,SAAS,CAACmC,MAAM,CAAC,IAAI;YAAC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChG,CAAC,eACNtC,OAAA;cAAKiC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAEvC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA,CAACF,kBAAkB;MACjBmE,eAAe,EAAC;IAAS;MAAA9B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACpC,EAAA,CA/PID,SAAmB;EAAA,QACEL,WAAW;AAAA;AAAAsE,EAAA,GADhCjE,SAAmB;AAiQzB,eAAeA,SAAS;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}