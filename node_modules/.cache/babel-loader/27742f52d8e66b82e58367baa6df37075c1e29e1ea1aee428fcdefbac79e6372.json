{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n\n// Create axios instance with default config\nconst apiClient = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  withCredentials: true,\n  // Important for HTTP-only cookies\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Newsletter API\nexport const newsletterAPI = {\n  // Get all newsletter subscribers\n  getSubscribers: async () => {\n    try {\n      const response = await apiClient.get('/api/newsletter/subscribers');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching newsletter subscribers:', error);\n      throw error;\n    }\n  },\n  // Subscribe to newsletter\n  subscribe: async email => {\n    try {\n      const response = await apiClient.post('/api/newsletter/subscribe', {\n        email\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error subscribing to newsletter:', error);\n      throw error;\n    }\n  },\n  // Unsubscribe from newsletter\n  unsubscribe: async email => {\n    try {\n      const response = await apiClient.post('/api/newsletter/unsubscribe', {\n        email\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error unsubscribing from newsletter:', error);\n      throw error;\n    }\n  },\n  // Delete subscriber (admin only)\n  deleteSubscriber: async id => {\n    try {\n      const response = await apiClient.delete(`/api/newsletter/subscribers/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error deleting newsletter subscriber:', error);\n      throw error;\n    }\n  },\n  // Export subscribers (admin only)\n  exportSubscribers: async () => {\n    try {\n      const response = await apiClient.get('/api/newsletter/export', {\n        responseType: 'blob'\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error exporting newsletter subscribers:', error);\n      throw error;\n    }\n  },\n  // Add subscriber (admin only) - alias for subscribe\n  addSubscriber: async email => {\n    try {\n      const response = await apiClient.post('/api/newsletter/subscribe', {\n        email\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error adding newsletter subscriber:', error);\n      throw error;\n    }\n  },\n  // Remove subscriber (admin only) - alias for deleteSubscriber\n  removeSubscriber: async id => {\n    try {\n      const response = await apiClient.delete(`/api/newsletter/subscribers/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error removing newsletter subscriber:', error);\n      throw error;\n    }\n  },\n  // Bulk import subscribers (admin only)\n  bulkImport: async emails => {\n    try {\n      const response = await apiClient.post('/api/newsletter/bulk-import', {\n        emails\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error bulk importing newsletter subscribers:', error);\n      throw error;\n    }\n  }\n};\n\n// General API utilities\nexport const api = {\n  get: url => apiClient.get(url),\n  post: (url, data) => apiClient.post(url, data),\n  put: (url, data) => apiClient.put(url, data),\n  delete: url => apiClient.delete(url)\n};\nexport default apiClient;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "apiClient", "create", "baseURL", "timeout", "withCredentials", "headers", "newsletterAPI", "getSubscribers", "response", "get", "data", "error", "console", "subscribe", "email", "post", "unsubscribe", "deleteSubscriber", "id", "delete", "exportSubscribers", "responseType", "addSubscriber", "removeSubscriber", "bulkImport", "emails", "api", "url", "put"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n\n// Create axios instance with default config\nconst apiClient = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  withCredentials: true, // Important for HTTP-only cookies\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Newsletter API\nexport const newsletterAPI = {\n  // Get all newsletter subscribers\n  getSubscribers: async () => {\n    try {\n      const response = await apiClient.get('/api/newsletter/subscribers');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching newsletter subscribers:', error);\n      throw error;\n    }\n  },\n\n  // Subscribe to newsletter\n  subscribe: async (email: string) => {\n    try {\n      const response = await apiClient.post('/api/newsletter/subscribe', { email });\n      return response.data;\n    } catch (error) {\n      console.error('Error subscribing to newsletter:', error);\n      throw error;\n    }\n  },\n\n  // Unsubscribe from newsletter\n  unsubscribe: async (email: string) => {\n    try {\n      const response = await apiClient.post('/api/newsletter/unsubscribe', { email });\n      return response.data;\n    } catch (error) {\n      console.error('Error unsubscribing from newsletter:', error);\n      throw error;\n    }\n  },\n\n  // Delete subscriber (admin only)\n  deleteSubscriber: async (id: number) => {\n    try {\n      const response = await apiClient.delete(`/api/newsletter/subscribers/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error deleting newsletter subscriber:', error);\n      throw error;\n    }\n  },\n\n  // Export subscribers (admin only)\n  exportSubscribers: async () => {\n    try {\n      const response = await apiClient.get('/api/newsletter/export', {\n        responseType: 'blob',\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error exporting newsletter subscribers:', error);\n      throw error;\n    }\n  },\n\n  // Add subscriber (admin only) - alias for subscribe\n  addSubscriber: async (email: string) => {\n    try {\n      const response = await apiClient.post('/api/newsletter/subscribe', { email });\n      return response.data;\n    } catch (error) {\n      console.error('Error adding newsletter subscriber:', error);\n      throw error;\n    }\n  },\n\n  // Remove subscriber (admin only) - alias for deleteSubscriber\n  removeSubscriber: async (id: number) => {\n    try {\n      const response = await apiClient.delete(`/api/newsletter/subscribers/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error removing newsletter subscriber:', error);\n      throw error;\n    }\n  },\n\n  // Bulk import subscribers (admin only)\n  bulkImport: async (emails: string[]) => {\n    try {\n      const response = await apiClient.post('/api/newsletter/bulk-import', { emails });\n      return response.data;\n    } catch (error) {\n      console.error('Error bulk importing newsletter subscribers:', error);\n      throw error;\n    }\n  },\n};\n\n// General API utilities\nexport const api = {\n  get: (url: string) => apiClient.get(url),\n  post: (url: string, data: any) => apiClient.post(url, data),\n  put: (url: string, data: any) => apiClient.put(url, data),\n  delete: (url: string) => apiClient.delete(url),\n};\n\nexport default apiClient;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;AAE7E;AACA,MAAMC,SAAS,GAAGL,KAAK,CAACM,MAAM,CAAC;EAC7BC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE,KAAK;EACdC,eAAe,EAAE,IAAI;EAAE;EACvBC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMC,aAAa,GAAG;EAC3B;EACAC,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMR,SAAS,CAACS,GAAG,CAAC,6BAA6B,CAAC;MACnE,OAAOD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAE,SAAS,EAAE,MAAOC,KAAa,IAAK;IAClC,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMR,SAAS,CAACe,IAAI,CAAC,2BAA2B,EAAE;QAAED;MAAM,CAAC,CAAC;MAC7E,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAK,WAAW,EAAE,MAAOF,KAAa,IAAK;IACpC,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMR,SAAS,CAACe,IAAI,CAAC,6BAA6B,EAAE;QAAED;MAAM,CAAC,CAAC;MAC/E,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAM,gBAAgB,EAAE,MAAOC,EAAU,IAAK;IACtC,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMR,SAAS,CAACmB,MAAM,CAAC,+BAA+BD,EAAE,EAAE,CAAC;MAC5E,OAAOV,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAS,iBAAiB,EAAE,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAMR,SAAS,CAACS,GAAG,CAAC,wBAAwB,EAAE;QAC7DY,YAAY,EAAE;MAChB,CAAC,CAAC;MACF,OAAOb,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAW,aAAa,EAAE,MAAOR,KAAa,IAAK;IACtC,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMR,SAAS,CAACe,IAAI,CAAC,2BAA2B,EAAE;QAAED;MAAM,CAAC,CAAC;MAC7E,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAY,gBAAgB,EAAE,MAAOL,EAAU,IAAK;IACtC,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMR,SAAS,CAACmB,MAAM,CAAC,+BAA+BD,EAAE,EAAE,CAAC;MAC5E,OAAOV,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAa,UAAU,EAAE,MAAOC,MAAgB,IAAK;IACtC,IAAI;MACF,MAAMjB,QAAQ,GAAG,MAAMR,SAAS,CAACe,IAAI,CAAC,6BAA6B,EAAE;QAAEU;MAAO,CAAC,CAAC;MAChF,OAAOjB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACpE,MAAMA,KAAK;IACb;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMe,GAAG,GAAG;EACjBjB,GAAG,EAAGkB,GAAW,IAAK3B,SAAS,CAACS,GAAG,CAACkB,GAAG,CAAC;EACxCZ,IAAI,EAAEA,CAACY,GAAW,EAAEjB,IAAS,KAAKV,SAAS,CAACe,IAAI,CAACY,GAAG,EAAEjB,IAAI,CAAC;EAC3DkB,GAAG,EAAEA,CAACD,GAAW,EAAEjB,IAAS,KAAKV,SAAS,CAAC4B,GAAG,CAACD,GAAG,EAAEjB,IAAI,CAAC;EACzDS,MAAM,EAAGQ,GAAW,IAAK3B,SAAS,CAACmB,MAAM,CAACQ,GAAG;AAC/C,CAAC;AAED,eAAe3B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}